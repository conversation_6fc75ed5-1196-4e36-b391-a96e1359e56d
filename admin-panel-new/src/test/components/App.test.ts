import { mount } from '@vue/test-utils'
import { createTesting<PERSON>inia } from '@pinia/testing'
import { createRouter, createWebHistory } from 'vue-router'
import { describe, it, expect, vi } from 'vitest'
import App from '@/App.vue'
import NotificationContainer from '@/components/NotificationContainer.vue'
import ModalContainer from '@/components/ModalContainer.vue'
import { useAuthStore } from '@/stores/auth'

// Мокаем маршруты для тестирования
const routes = [
  { path: '/dashboard', name: 'Dashboard', meta: { title: 'Дашборд', icon: 'DashboardIcon' }, component: { template: '<div>Dashboard page</div>' } },
  { path: '/login', name: '<PERSON><PERSON>', meta: { title: 'Вход', icon: 'UsersIcon' }, component: { template: '<div>Login page</div>' } }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

describe('App.vue', () => {
  it('рендерит контейнеры уведомлений и модалей', async () => {
    const wrapper = mount(App, {
      global: {
        plugins: [createTestingPinia({ stubActions: false }), router]
      }
    })
    expect(wrapper.findComponent(NotificationContainer).exists()).toBe(true)
    expect(wrapper.findComponent(ModalContainer).exists()).toBe(true)
  })

  it('отображает сайдбар и хедер только для авторизованных', async () => {
    // Создаём Pinia и мок authStore
    const pinia = createTestingPinia({ stubActions: false })
    const wrapper = mount(App, {
      global: {
        plugins: [pinia, router]
      }
    })
    // По умолчанию не авторизован
    expect(wrapper.find('header').exists()).toBe(false)
    expect(wrapper.find('.fixed.inset-y-0').exists()).toBe(false)
    // Эмулируем авторизацию через useAuthStore
    const authStore = useAuthStore()
vi.mocked(authStore).isAuthenticated = true
    await wrapper.vm.$nextTick()
    expect(wrapper.find('header').exists()).toBe(true)
    expect(wrapper.find('.fixed.inset-y-0').exists()).toBe(true)
  })

  it('корректно рендерит дочерние страницы через <router-view />', async () => {
    router.push('/dashboard')
    await router.isReady()
    const wrapper = mount(App, {
      global: {
        plugins: [createTestingPinia({ stubActions: false }), router]
      }
    })
    expect(wrapper.html()).toContain('Dashboard page')
  })
})