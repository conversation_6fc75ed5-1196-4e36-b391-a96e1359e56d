import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import NotificationContainer from '@/components/NotificationContainer.vue'

// Мокаем стор уведомлений
vi.mock('@/stores', () => ({
  useNotificationStore: vi.fn()
}))

// Мокаем иконки
vi.mock('~icons/lucide/check-circle', () => ({ default: { name: 'CheckCircleIcon' } }))
vi.mock('~icons/lucide/alert-circle', () => ({ default: { name: 'AlertCircleIcon' } }))
vi.mock('~icons/lucide/info', () => ({ default: { name: 'InfoIcon' } }))
vi.mock('~icons/lucide/x-circle', () => ({ default: { name: 'XCircleIcon' } }))
vi.mock('~icons/lucide/x', () => ({ default: { name: 'XIcon' } }))

describe('NotificationContainer.vue', () => {
  let wrapper: any
  let mockNotificationStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок стора уведомлений
    mockNotificationStore = {
      notifications: [
        {
          id: 'notif-1',
          type: 'success',
          title: 'Успешно!',
          message: 'Операция выполнена успешно',
          duration: 5000,
          actions: [
            {
              label: 'Отменить',
              action: vi.fn()
            }
          ]
        },
        {
          id: 'notif-2',
          type: 'error',
          title: 'Ошибка!',
          message: 'Произошла ошибка при выполнении операции',
          duration: 0, // Постоянное уведомление
          actions: []
        },
        {
          id: 'notif-3',
          type: 'warning',
          title: 'Предупреждение',
          message: 'Проверьте введенные данные',
          duration: 3000,
          actions: [
            {
              label: 'Исправить',
              action: vi.fn()
            },
            {
              label: 'Игнорировать',
              action: vi.fn()
            }
          ]
        },
        {
          id: 'notif-4',
          type: 'info',
          title: 'Информация',
          message: 'Новая версия приложения доступна',
          duration: 4000,
          actions: []
        }
      ],
      addNotification: vi.fn(),
      removeNotification: vi.fn(),
      clearAll: vi.fn(),
      error: vi.fn()
    }

    // Настройка мока стора
    const { useNotificationStore } = await import('@/stores')
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(NotificationContainer, {
      global: {
        stubs: {
          CheckCircleIcon: true,
          AlertCircleIcon: true,
          InfoIcon: true,
          XCircleIcon: true,
          XIcon: true,
          TransitionGroup: {
            template: '<div><slot /></div>'
          }
        }
      }
    })
  })

  describe('Рендеринг уведомлений', () => {
    it('отображает все активные уведомления', () => {
      const notifications = wrapper.findAll('[data-testid^="notification-"], .bg-white')
      expect(notifications.length).toBe(4)
    })

    it('отображает заголовки уведомлений', () => {
      const titles = wrapper.findAll('.font-medium')
      expect(titles[0].text()).toBe('Успешно!')
      expect(titles[1].text()).toBe('Ошибка!')
      expect(titles[2].text()).toBe('Предупреждение')
      expect(titles[3].text()).toBe('Информация')
    })

    it('отображает сообщения уведомлений', () => {
      expect(wrapper.text()).toContain('Операция выполнена успешно')
      expect(wrapper.text()).toContain('Произошла ошибка при выполнении операции')
      expect(wrapper.text()).toContain('Проверьте введенные данные')
      expect(wrapper.text()).toContain('Новая версия приложения доступна')
    })

    it('отображает иконки для каждого типа уведомления', () => {
      const icons = wrapper.findAllComponents({ name: /Icon$/ })
      expect(icons.length).toBeGreaterThan(0)
    })

    it('отображает кнопки закрытия для всех уведомлений', () => {
      const closeButtons = wrapper.findAll('button')
      // Минимум 4 кнопки закрытия + кнопки действий
      expect(closeButtons.length).toBeGreaterThanOrEqual(4)
    })
  })

  describe('Типы уведомлений', () => {
    it('применяет правильные классы для success уведомлений', () => {
      const successNotification = wrapper.findAll('.bg-white')[0]
      // Проверяем, что применяются соответствующие классы
      expect(successNotification.exists()).toBe(true)
    })

    it('применяет правильные классы для error уведомлений', () => {
      const errorNotification = wrapper.findAll('.bg-white')[1]
      expect(errorNotification.exists()).toBe(true)
    })

    it('применяет правильные классы для warning уведомлений', () => {
      const warningNotification = wrapper.findAll('.bg-white')[2]
      expect(warningNotification.exists()).toBe(true)
    })

    it('применяет правильные классы для info уведомлений', () => {
      const infoNotification = wrapper.findAll('.bg-white')[3]
      expect(infoNotification.exists()).toBe(true)
    })
  })

  describe('Действия уведомлений', () => {
    it('отображает кнопки действий когда они есть', () => {
      const actionButtons = wrapper.findAll('button').filter((btn: { text: () => string }) => 
        btn.text() === 'Отменить' || 
        btn.text() === 'Исправить' || 
        btn.text() === 'Игнорировать'
      )
      expect(actionButtons.length).toBe(3) // Отменить + Исправить + Игнорировать
    })

    it('выполняет действие при клике на кнопку действия', async () => {
      const cancelButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Отменить')
      if (cancelButton) {
        await cancelButton.trigger('click')
        expect(mockNotificationStore.notifications[0].actions[0].action).toHaveBeenCalled()
      }
    })

    it('не отображает секцию действий когда действий нет', () => {
      // Проверяем, что для уведомлений без действий секция не отображается
      const notifications = wrapper.findAll('.bg-white')
      const errorNotification = notifications[1] // error уведомление без действий
      const actionSection = errorNotification.find('.mt-3.flex.space-x-2')
      expect(actionSection.exists()).toBe(false)
    })
  })

  describe('Закрытие уведомлений', () => {
    it('закрывает уведомление при клике на кнопку закрытия', async () => {
      const closeButtons = wrapper.findAll('button').filter((btn: { find: (arg0: string) => { (): any; new(): any; text: { (): string; new(): any } } }) => 
        btn.find('.sr-only')?.text() === 'Закрыть'
      )
      
      if (closeButtons.length > 0) {
        await closeButtons[0].trigger('click')
        expect(mockNotificationStore.removeNotification).toHaveBeenCalledWith('notif-1')
      }
    })

    it('вызывает removeNotification с правильным ID', async () => {
      const closeButtons = wrapper.findAll('button').filter((btn: { find: (arg0: string) => { (): any; new(): any; text: { (): string; new(): any } } }) => 
        btn.find('.sr-only')?.text() === 'Закрыть'
      )
      
      if (closeButtons.length > 1) {
        await closeButtons[1].trigger('click')
        expect(mockNotificationStore.removeNotification).toHaveBeenCalledWith('notif-2')
      }
    })
  })

  describe('Анимации и переходы', () => {
    it('использует TransitionGroup для анимации', () => {
      const transitionGroup = wrapper.findComponent({ name: 'TransitionGroup' })
      expect(transitionGroup.exists()).toBe(true)
    })

    it('применяет правильное имя анимации', () => {
      const transitionGroup = wrapper.findComponent({ name: 'TransitionGroup' })
      if (transitionGroup.exists()) {
        expect(transitionGroup.props('name')).toBe('notification')
      }
    })
  })

  describe('Позиционирование', () => {
    it('имеет правильные классы позиционирования', () => {
      const container = wrapper.find('.fixed.top-4.right-4.z-50')
      expect(container.exists()).toBe(true)
    })

    it('имеет правильную максимальную ширину', () => {
      const container = wrapper.find('.max-w-sm.w-full')
      expect(container.exists()).toBe(true)
    })

    it('имеет правильные отступы между уведомлениями', () => {
      const spacingContainer = wrapper.find('.space-y-4')
      expect(spacingContainer.exists()).toBe(true)
    })
  })

  describe('Доступность', () => {
    it('содержит screen reader текст для кнопок закрытия', () => {
      const srOnlyElements = wrapper.findAll('.sr-only')
      const closeLabels = srOnlyElements.filter((el: { text: () => string }) => el.text() === 'Закрыть')
      expect(closeLabels.length).toBeGreaterThan(0)
    })

    it('поддерживает навигацию с клавиатуры', () => {
      const buttons = wrapper.findAll('button')
      buttons.forEach((button: { classes: () => any }) => {
        expect(button.classes()).toContain('focus:outline-none')
      })
    })

    it('имеет правильные aria-атрибуты', () => {
      // Проверяем, что уведомления имеют соответствующие aria-атрибуты
      const notifications = wrapper.findAll('.bg-white')
      expect(notifications.length).toBeGreaterThan(0)
    })
  })

  describe('Методы компонента', () => {
    it('имеет метод getNotificationClasses', () => {
      expect(typeof wrapper.vm.getNotificationClasses).toBe('function')
    })

    it('имеет метод getIcon', () => {
      expect(typeof wrapper.vm.getIcon).toBe('function')
    })

    it('имеет метод getTitleClasses', () => {
      expect(typeof wrapper.vm.getTitleClasses).toBe('function')
    })

    it('имеет метод getMessageClasses', () => {
      expect(typeof wrapper.vm.getMessageClasses).toBe('function')
    })

    it('имеет метод getActionClasses', () => {
      expect(typeof wrapper.vm.getActionClasses).toBe('function')
    })

    it('имеет метод removeNotification', () => {
      expect(typeof wrapper.vm.removeNotification).toBe('function')
    })
  })

  describe('Обработка различных типов', () => {
    it('возвращает правильную иконку для success', () => {
      const icon = wrapper.vm.getIcon('success')
      expect(icon).toBeDefined()
    })

    it('возвращает правильную иконку для error', () => {
      const icon = wrapper.vm.getIcon('error')
      expect(icon).toBeDefined()
    })

    it('возвращает правильную иконку для warning', () => {
      const icon = wrapper.vm.getIcon('warning')
      expect(icon).toBeDefined()
    })

    it('возвращает правильную иконку для info', () => {
      const icon = wrapper.vm.getIcon('info')
      expect(icon).toBeDefined()
    })

    it('обрабатывает неизвестный тип уведомления', () => {
      const icon = wrapper.vm.getIcon('unknown')
      expect(icon).toBeDefined() // Должна быть fallback иконка
    })
  })

  describe('Реактивность', () => {
    it('реагирует на добавление новых уведомлений', async () => {
      const initialCount = wrapper.findAll('.bg-white').length
      
      // Добавляем новое уведомление
      mockNotificationStore.notifications.push({
        id: 'notif-5',
        type: 'success',
        title: 'Новое уведомление',
        message: 'Тестовое сообщение',
        duration: 3000,
        actions: []
      })

      await wrapper.vm.$nextTick()
      
      const newCount = wrapper.findAll('.bg-white').length
      expect(newCount).toBe(initialCount + 1)
    })

    it('реагирует на удаление уведомлений', async () => {
      const initialCount = wrapper.findAll('.bg-white').length
      
      // Удаляем уведомление
      mockNotificationStore.notifications.pop()

      await wrapper.vm.$nextTick()
      
      const newCount = wrapper.findAll('.bg-white').length
      expect(newCount).toBe(initialCount - 1)
    })
  })

  describe('Обработка пустого состояния', () => {
    it('корректно обрабатывает отсутствие уведомлений', async () => {
      mockNotificationStore.notifications = []
      await wrapper.vm.$nextTick()
      
      const notifications = wrapper.findAll('.bg-white')
      expect(notifications.length).toBe(0)
    })
  })

  describe('Стилизация', () => {
    it('применяет базовые стили к уведомлениям', () => {
      const notifications = wrapper.findAll('.bg-white.rounded-lg.shadow-lg')
      expect(notifications.length).toBeGreaterThan(0)
    })

    it('применяет правильные стили к кнопкам действий', () => {
      const actionButtons = wrapper.findAll('button').filter((btn: { classes: () => string | string[] }) => 
        btn.classes().includes('underline')
      )
      expect(actionButtons.length).toBeGreaterThan(0)
    })

    it('применяет hover эффекты', () => {
      const buttons = wrapper.findAll('button')
      buttons.forEach((button: { classes: () => any[] }) => {
        expect(button.classes().some((cls: string | string[]) => cls.includes('hover:'))).toBe(true)
      })
    })
  })
})