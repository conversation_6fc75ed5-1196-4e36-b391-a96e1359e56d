import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import MultipleBookingsModal from '@/components/modals/MultipleBookingsModal.vue'

// Мокаем stores
vi.mock('@/stores', () => ({
  useBookingStore: vi.fn(),
  useServiceStore: vi.fn(),
  useStaffStore: vi.fn(),
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))

// Мокаем иконки
vi.mock('~icons/lucide/users', () => ({ default: { name: 'UsersIcon' } }))
vi.mock('~icons/lucide/calendar', () => ({ default: { name: 'CalendarIcon' } }))
vi.mock('~icons/lucide/clock', () => ({ default: { name: 'ClockIcon' } }))
vi.mock('~icons/lucide/plus', () => ({ default: { name: 'PlusIcon' } }))
vi.mock('~icons/lucide/trash-2', () => ({ default: { name: 'TrashIcon' } }))
vi.mock('~icons/lucide/x', () => ({ default: { name: 'XIcon' } }))

describe('MultipleBookingsModal.vue', () => {
  let wrapper: any
  let mockBookingStore: any
  let mockServiceStore: any
  let mockStaffStore: any
  let mockModalStore: any
  let mockNotificationStore: any

  const mockServices = [
    { id: 1, name: 'Массаж', category: 'spa', price: 300, duration: 60 },
    { id: 2, name: 'СПА процедуры', category: 'spa', price: 500, duration: 90 },
    { id: 3, name: 'Завтрак', category: 'food', price: 150, duration: 30 }
  ]

  const mockStaff = [
    { id: 1, name: 'Мария Иванова', position: 'Массажист', services: [1, 2] },
    { id: 2, name: 'Анна Петрова', position: 'Косметолог', services: [2] },
    { id: 3, name: 'Петр Сидоров', position: 'Администратор', services: [3] }
  ]

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок booking store
    mockBookingStore = {
      loading: false,
      error: null,
      createMultipleBookings: vi.fn().mockResolvedValue(true),
      getAvailableSlots: vi.fn().mockResolvedValue([
        { date: '2024-01-15', time: '09:00', available: true },
        { date: '2024-01-15', time: '10:00', available: true },
        { date: '2024-01-15', time: '11:00', available: true }
      ])
    }

    // Мок service store
    mockServiceStore = {
      loading: false,
      services: mockServices,
      categories: [
        { id: 1, name: 'SPA услуги', slug: 'spa' },
        { id: 2, name: 'Питание', slug: 'food' }
      ],
      fetchServices: vi.fn()
    }

    // Мок staff store
    mockStaffStore = {
      loading: false,
      staff: mockStaff,
      fetchStaff: vi.fn()
    }

    // Мок modal store
    mockModalStore = {
      isOpen: true,
      title: 'Групповое бронирование',
      props: {
        date: '2024-01-15',
        time: '10:00'
      },
      close: vi.fn()
    }

    // Мок notification store
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn()
    }

    // Настройка моков stores
    const { useBookingStore, useServiceStore, useStaffStore, useModalStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useServiceStore).mockReturnValue(mockServiceStore)
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)
    vi.mocked(useModalStore).mockReturnValue(mockModalStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(MultipleBookingsModal, {
      global: {
        stubs: {
          teleport: true
        }
      }
    })
  })

  describe('Рендеринг компонента', () => {
    it('отображает модальное окно когда открыто', () => {
      expect(wrapper.find('.modal').exists()).toBe(true)
    })

    it('отображает правильный заголовок модального окна', () => {
      expect(wrapper.text()).toContain('Групповое бронирование')
    })

    it('отображает поля для общей информации', () => {
      expect(wrapper.find('input[type="date"]').exists()).toBe(true)
      expect(wrapper.find('input[type="time"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="Количество участников"]').exists()).toBe(true)
    })

    it('отображает кнопку добавления нового бронирования', () => {
      const buttons = wrapper.findAll('button')
      const hasAddButton = buttons.some((button: { text: () => string | string[] }) => button.text().includes('Добавить участника'))
      expect(hasAddButton).toBe(true)
    })

    it('отображает кнопки действий', () => {
      // Проверяем наличие кнопки "Отмена"
      const cancelButton = wrapper.find('button[type="button"]')
      expect(cancelButton.exists()).toBe(true)
      expect(cancelButton.text()).toContain('Отмена')
      
      // Проверяем наличие кнопки "Выполнить операцию"
      const submitButton = wrapper.find('button:not([type="button"])')
      expect(submitButton.exists()).toBe(true)
    })
  })

  describe('Управление бронированиями', () => {
    it('отображает выбранные бронирования', () => {
      // Проверяем, что отображается заголовок с количеством выбранных бронирований
      expect(wrapper.text()).toContain('Выбрано')
      expect(wrapper.text()).toContain('бронирований')
    })

    it('позволяет удалить бронирование из списка', async () => {
      // Ищем кнопки удаления бронирований
      const deleteButtons = wrapper.findAll('button[title="Удалить из списка"]')
      if (deleteButtons.length > 0) {
        await deleteButtons[0].trigger('click')
        // Проверяем, что функция удаления была вызвана
        expect(wrapper.emitted('remove-booking')).toBeTruthy()
      }
    })

    it('показывает селект для назначения сотрудника при выборе операции', async () => {
      // Устанавливаем операцию назначения сотрудника
      const operationSelect = wrapper.find('select')
      if (operationSelect.exists()) {
        await operationSelect.setValue('assign_staff')
        await wrapper.vm.$nextTick()
      }

      // Проверяем, что появился блок с селектом для выбора сотрудника
      const assignStaffDiv = wrapper.find('div').filter((div: { text: () => string | string[] }) => 
        div.text().includes('Сотрудник')
      )
      expect(assignStaffDiv.length).toBeGreaterThan(0)
    })
  })

  describe('Валидация формы', () => {
    it('проверяет обязательные поля перед отправкой', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit')

      // Проверяем, что форма не отправляется без заполненных полей
      expect(mockBookingStore.createMultipleBookings).not.toHaveBeenCalled()
    })

    it('проверяет валидацию формы операций', async () => {
      // Проверяем, что без выбора операции кнопка отправки недоступна
      const buttons = wrapper.findAll('button')
      const submitButton = buttons.find((button: { text: () => string | string[] }) => button.text().includes('Применить') || button.text().includes('Выполнить'))
      
      if (submitButton) {
        expect(submitButton.attributes('disabled')).toBeDefined()
      }
    })

    it('активирует кнопку отправки при выборе операции', async () => {
      // Выбираем операцию
      const operationSelect = wrapper.find('select')
      if (operationSelect.exists()) {
        await operationSelect.setValue('confirm')
      }

      // Проверяем, что кнопка стала активной
      const buttons = wrapper.findAll('button')
      const submitButton = buttons.find((button: { text: () => string | string[] }) => button.text().includes('Применить') || button.text().includes('Выполнить'))
      
      if (submitButton) {
        expect(submitButton.attributes('disabled')).toBeUndefined()
      }
    })
  })

  describe('Отправка формы', () => {
    it('отправляет данные при корректном заполнении', async () => {
      // Выбираем операцию
      const operationSelect = wrapper.find('select')
      if (operationSelect.exists()) {
        await operationSelect.setValue('confirm')
      }

      // Нажимаем кнопку отправки
      const buttons = wrapper.findAll('button')
      const submitButton = buttons.find((button: { text: () => string | string[] }) => button.text().includes('Применить') || button.text().includes('Выполнить'))
      
      if (submitButton && !submitButton.attributes('disabled')) {
        await submitButton.trigger('click')
        // Проверяем, что событие отправки было вызвано
        expect(wrapper.emitted('submit')).toBeTruthy()
      }
    })

    it('обрабатывает ошибку при создании группового бронирования', async () => {
      mockBookingStore.createMultipleBookings.mockRejectedValue(new Error('Ошибка сервера'))
      
      // Заполняем минимальные данные для отправки
      const dateInput = wrapper.find('input[type="date"]')
      if (dateInput.exists()) await dateInput.setValue('2024-01-15')
      
      const form = wrapper.find('form')
      await form.trigger('submit')

      expect(mockNotificationStore.error).toHaveBeenCalledWith('Ошибка при создании группового бронирования')
    })
  })

  describe('Состояние загрузки', () => {
    it('отображает состояние загрузки при отправке формы', async () => {
      mockBookingStore.loading = true
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
      expect(submitButton.text()).toContain('Создание...')
    })

    it('блокирует кнопки во время загрузки', async () => {
      mockBookingStore.loading = true
      await wrapper.vm.$nextTick()

      const buttons = wrapper.findAll('button')
      buttons.forEach((button: { attributes: (arg0: string) => string; text: () => string | string[] }) => {
        if (button.attributes('type') !== 'button' || button.text().includes('Отмена')) {
          return // Кнопка отмены должна оставаться активной
        }
        expect(button.attributes('disabled')).toBeDefined()
      })
    })
  })

  describe('Закрытие модального окна', () => {
    it('закрывает модальное окно при нажатии кнопки отмены', async () => {
      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find((button: { text: () => string | string[] }) => button.text().includes('Отмена'))
      if (cancelButton) {
        await cancelButton.trigger('click')
        expect(mockModalStore.close).toHaveBeenCalled()
      }
    })

    it('закрывает модальное окно при нажатии на крестик', async () => {
      const closeButton = wrapper.find('button[aria-label="Закрыть"]')
      await closeButton.trigger('click')

      expect(mockModalStore.close).toHaveBeenCalled()
    })
  })
})