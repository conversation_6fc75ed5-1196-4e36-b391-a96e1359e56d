import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ReportGeneratorModal from '@/components/modals/ReportGeneratorModal.vue'

// Мокаем сторы
vi.mock('@/stores', () => ({
  useStaffStore: vi.fn(),
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn()
}))

describe('ReportGeneratorModal.vue', () => {
  let wrapper: any
  let mockStaffStore: any
  let mockModalStore: any
  let mockNotificationStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок стора сотрудников
    mockStaffStore = {
      loading: false,
      activeStaff: [
        { id: 1, name: 'Иван Петров', position: 'Массажист' },
        { id: 2, name: 'Мария Сидорова', position: 'Администратор' },
        { id: 3, name: 'Анна Козлова', position: 'Косметолог' }
      ],
      fetchStaff: vi.fn()
    }

    // Мок стора модальных окон
    mockModalStore = {
      closeModal: vi.fn()
    }

    // Мок стора уведомлений
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn()
    }

    // Настройка моков сторов
    const { useStaffStore, useModalStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)
    vi.mocked(useModalStore).mockReturnValue(mockModalStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(ReportGeneratorModal, {
      global: {
        stubs: {
          Teleport: true
        }
      }
    })
  })

  describe('Рендеринг формы', () => {
    it('отображает заголовок и описание', () => {
      expect(wrapper.text()).toContain('Генератор отчетов')
      expect(wrapper.text()).toContain('Создайте отчет с настраиваемыми параметрами')
    })

    it('отображает селект типа отчета', () => {
      const reportTypeSelect = wrapper.find('select')
      expect(reportTypeSelect.exists()).toBe(true)
      
      const options = reportTypeSelect.findAll('option')
      expect(options.length).toBeGreaterThan(6)
      
      const optionTexts = options.map((option: any) => option.text())
      expect(optionTexts).toContain('Отчет по бронированиям')
      expect(optionTexts).toContain('Отчет по доходам')
      expect(optionTexts).toContain('Отчет по услугам')
      expect(optionTexts).toContain('Отчет по сотрудникам')
      expect(optionTexts).toContain('Отчет по гостям')
      expect(optionTexts).toContain('Аналитический отчет')
    })

    it('отображает радиокнопки для выбора периода', () => {
      const radioButtons = wrapper.findAll('input[type="radio"]')
      expect(radioButtons.length).toBeGreaterThan(4)
      
      const labels = wrapper.findAll('label')
      const labelTexts = labels.map((label: any) => label.text())
      expect(labelTexts.some((text: string) => text.includes('Сегодня'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Эта неделя'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Этот месяц'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Произвольный'))).toBe(true)
    })

    it('отображает поля для выбора произвольного периода', async () => {
      const customRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'custom'
      )
      
      if (customRadio) {
        await customRadio.setChecked()
        await wrapper.vm.$nextTick()
        
        const dateInputs = wrapper.findAll('input[type="date"]')
        expect(dateInputs.length).toBe(2)
      }
    })

    it('отображает кнопки действий', () => {
      const generateButton = wrapper.find('button[type="submit"]')
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      const previewButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Предварительный просмотр'))
      
      expect(generateButton.exists()).toBe(true)
      expect(cancelButton).toBeTruthy()
      expect(previewButton).toBeTruthy()
    })
  })

  describe('Выбор типа отчета', () => {
    it('позволяет выбрать тип отчета', async () => {
      const select = wrapper.find('select')
      await select.setValue('bookings')
      
      expect(select.element.value).toBe('bookings')
    })

    it('показывает соответствующие фильтры для типа отчета', async () => {
      const select = wrapper.find('select')
      await select.setValue('bookings')
      
      await wrapper.vm.$nextTick()
      
      // Для отчета по бронированиям должны появиться фильтры статуса
      const statusSelects = wrapper.findAll('select')
      const hasStatusFilter = statusSelects.some((sel: any) => {
        const options = sel.findAll('option')
        return options.some((opt: any) => opt.text().includes('Подтверждено'))
      })
      expect(hasStatusFilter).toBe(true)
    })

    it('показывает фильтр сотрудников для соответствующих типов отчетов', async () => {
      const select = wrapper.find('select')
      await select.setValue('staff')
      
      await wrapper.vm.$nextTick()
      
      // Должен появиться селект сотрудников
      const staffSelects = wrapper.findAll('select')
      const hasStaffFilter = staffSelects.some((sel: any) => {
        const options = sel.findAll('option')
        return options.some((opt: any) => opt.text().includes('Иван Петров'))
      })
      expect(hasStaffFilter).toBe(true)
    })
  })

  describe('Выбор периода времени', () => {
    it('позволяет выбрать предустановленный период', async () => {
      const todayRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'today'
      )
      
      if (todayRadio) {
        await todayRadio.setChecked()
        expect(todayRadio.element.checked).toBe(true)
      }
    })

    it('позволяет выбрать произвольный период', async () => {
      const customRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'custom'
      )
      
      if (customRadio) {
        await customRadio.setChecked()
        expect(customRadio.element.checked).toBe(true)
        
        await wrapper.vm.$nextTick()
        
        // Поля дат должны стать доступными
        const dateInputs = wrapper.findAll('input[type="date"]')
        expect(dateInputs.length).toBe(2)
        expect(dateInputs[0].attributes('required')).toBeDefined()
        expect(dateInputs[1].attributes('required')).toBeDefined()
      }
    })

    it('валидирует произвольный период', async () => {
      const customRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'custom'
      )
      
      if (customRadio) {
        await customRadio.setChecked()
        await wrapper.vm.$nextTick()
        
        const dateInputs = wrapper.findAll('input[type="date"]')
        if (dateInputs.length >= 2) {
          const startDate = dateInputs[0]
          const endDate = dateInputs[1]
          
          // Устанавливаем неправильный период (конец раньше начала)
          await startDate.setValue('2024-01-31')
          await endDate.setValue('2024-01-01')
          
          // Проверяем, что endDate имеет min атрибут
          expect(endDate.attributes('min')).toBe('2024-01-31')
        }
      }
    })
  })

  describe('Параметры отчета', () => {
    it('отображает чекбоксы для дополнительных опций', () => {
      const checkboxes = wrapper.findAll('input[type="checkbox"]')
      expect(checkboxes.length).toBeGreaterThan(3)
      
      const labels = wrapper.findAll('label')
      const labelTexts = labels.map((label: any) => label.text())
      expect(labelTexts.some((text: string) => text.includes('Включить графики и диаграммы'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Включить детальную информацию'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Включить сводную информацию'))).toBe(true)
    })

    it('позволяет выбрать формат экспорта', () => {
      const formatRadios = wrapper.findAll('input[type="radio"]').filter((radio: any) => 
        ['pdf', 'excel', 'csv', 'json'].includes(radio.element.value)
      )
      
      expect(formatRadios.length).toBe(4)
    })

    it('показывает опции отправки по email', async () => {
      const emailCheckbox = wrapper.findAll('input[type="checkbox"]').find((cb: any) => {
        const label = cb.element.nextElementSibling
        return label && label.textContent?.includes('Отправить отчет на email')
      })
      
      if (emailCheckbox) {
        await emailCheckbox.setChecked(true)
        await wrapper.vm.$nextTick()
        
        const emailInput = wrapper.find('input[placeholder*="email"]')
        expect(emailInput.exists()).toBe(true)
      }
    })
  })

  describe('Предварительный просмотр', () => {
    it('показывает превью параметров отчета', async () => {
      const select = wrapper.find('select')
      await select.setValue('analytics')
      
      await wrapper.vm.$nextTick()
      
      // Проверяем наличие превью
      const preview = wrapper.find('.bg-green-50')
      expect(preview.exists()).toBe(true)
      expect(preview.text()).toContain('Предварительный просмотр')
      expect(preview.text()).toContain('Аналитический отчет')
    })

    it('обновляет превью при изменении параметров', async () => {
      const select = wrapper.find('select')
      await select.setValue('bookings')
      
      await wrapper.vm.$nextTick()
      
      const preview = wrapper.find('.bg-green-50')
      if (preview.exists()) {
        expect(preview.text()).toContain('Отчет по бронированиям')
      }
    })

    it('показывает кнопку предварительного просмотра', async () => {
      const previewButton = wrapper.findAll('button').find((btn: any) => 
        btn.text().includes('Предварительный просмотр')
      )
      
      if (previewButton) {
        await previewButton.trigger('click')
        expect(mockNotificationStore.info).toHaveBeenCalledWith(
          'Информация', 
          'Функция предварительного просмотра будет доступна в следующей версии'
        )
      }
    })
  })

  describe('Генерация отчет��', () => {
    it('генерирует отчет с правильными параметрами', async () => {
      // Заполняем форму
      const select = wrapper.find('select')
      await select.setValue('bookings')
      
      const todayRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'today'
      )
      if (todayRadio) {
        await todayRadio.setChecked()
      }
      
      const pdfRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'pdf'
      )
      if (pdfRadio) {
        await pdfRadio.setChecked()
      }
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.success).toHaveBeenCalledWith(
        'Успешно', 
        'Отчет сгенерирован и будет доступен для скачивания'
      )
    })

    it('показывает уведомление об успешной генерации', async () => {
      // Заполняем и отправляем форму
      const select = wrapper.find('select')
      await select.setValue('revenue')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.success).toHaveBeenCalledWith(
        'Успешно', 
        'Отчет сгенерирован и будет доступен для скачивания'
      )
    })

    it('закрывает модальное окно после успешной генерации', async () => {
      // Заполняем и отправляем форму
      const select = wrapper.find('select')
      await select.setValue('services')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockModalStore.closeModal).toHaveBeenCalledWith('report-generator')
    })
  })

  describe('Обработка ошибок', () => {
    it('отображает состояние загрузки', async () => {
      wrapper.vm.generating = true
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
      expect(submitButton.text()).toContain('Генерация...')
    })

    it('валидирует обязательные поля', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Без выбора типа отчета форма не должна отправляться
      expect(mockNotificationStore.success).not.toHaveBeenCalled()
    })

    it('валидирует произвольный период дат', async () => {
      const select = wrapper.find('select')
      await select.setValue('analytics')
      
      const customRadio = wrapper.findAll('input[type="radio"]').find((radio: any) => 
        radio.element.value === 'custom'
      )
      
      if (customRadio) {
        await customRadio.setChecked()
        await wrapper.vm.$nextTick()
        
        // Не заполняем даты
        const form = wrapper.find('form')
        await form.trigger('submit')
        
        // Форма не должна отправляться без дат
        expect(mockNotificationStore.success).not.toHaveBeenCalled()
      }
    })
  })

  describe('Закрытие модального окна', () => {
    it('закрывается при клике на кнопку отмены', async () => {
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      if (cancelButton) {
        await cancelButton.trigger('click')
        expect(mockModalStore.closeModal).toHaveBeenCalledWith('report-generator')
      }
    })
  })

  describe('Инициализация компонента', () => {
    it('инициали��ирует форму с значениями по умолчанию', () => {
      expect(wrapper.vm.form.reportType).toBe('')
      expect(wrapper.vm.form.dateRange).toBe('month')
      expect(wrapper.vm.form.exportFormat).toBe('pdf')
      expect(wrapper.vm.form.includeCharts).toBe(true)
      expect(wrapper.vm.form.includeDetails).toBe(true)
      expect(wrapper.vm.form.includeSummary).toBe(true)
    })

    it('загружает данные сотрудников', () => {
      expect(mockStaffStore.fetchStaff).toHaveBeenCalled()
    })

    it('устанавливает даты по умолчанию', () => {
      expect(wrapper.vm.form.startDate).toBeTruthy()
      expect(wrapper.vm.form.endDate).toBeTruthy()
    })
  })

  describe('Адаптивность', () => {
    it('корректно отображается на мобильных устройствах', async () => {
      // Симуляция мобильного экрана
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      await wrapper.vm.$nextTick()
      
      // Проверяем адаптивные классы
      const gridElements = wrapper.findAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })

  describe('Доступность', () => {
    it('имеет правильные лейблы для полей', () => {
      const labels = wrapper.findAll('label')
      expect(labels.length).toBeGreaterThan(0)
      
      const labelTexts = labels.map((label: any) => label.text())
      expect(labelTexts.some((text: string) => text.includes('Тип отчета'))).toBe(true)
      expect(labelTexts.some((text: string) => text.includes('Формат экспорта'))).toBe(true)
    })

    it('поддерживает навигацию с клавиатуры', async () => {
      const firstInput = wrapper.find('select')
      if (firstInput.exists()) {
        await firstInput.trigger('focus')
        // В реальном браузере элемент получил бы фокус
        expect(firstInput.exists()).toBe(true)
      }
    })
  })

  describe('Методы компонента', () => {
    it('имеет метод getReportTypeLabel', () => {
      expect(wrapper.vm.getReportTypeLabel('bookings')).toBe('Отчет по бронированиям')
      expect(wrapper.vm.getReportTypeLabel('revenue')).toBe('Отчет по доходам')
      expect(wrapper.vm.getReportTypeLabel('analytics')).toBe('Аналитический отчет')
    })

    it('имеет метод getDateRangeLabel', () => {
      wrapper.vm.form.dateRange = 'today'
      expect(wrapper.vm.getDateRangeLabel()).toBe('Сегодня')
      
      wrapper.vm.form.dateRange = 'week'
      expect(wrapper.vm.getDateRangeLabel()).toBe('Эта неделя')
      
      wrapper.vm.form.dateRange = 'month'
      expect(wrapper.vm.getDateRangeLabel()).toBe('Этот месяц')
    })

    it('имеет метод getStatusLabel', () => {
      expect(wrapper.vm.getStatusLabel('confirmed')).toBe('Подтверждено')
      expect(wrapper.vm.getStatusLabel('pending')).toBe('Ожидает подтверждения')
      expect(wrapper.vm.getStatusLabel('cancelled')).toBe('Отменено')
    })
  })
})