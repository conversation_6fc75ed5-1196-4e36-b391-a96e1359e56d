import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ServiceFormModal from '@/components/modals/ServiceFormModal.vue'

// Мокаем сторы
vi.mock('@/stores', () => ({
  useServiceStore: vi.fn(),
  useNotificationStore: vi.fn()
}))

describe('ServiceFormModal.vue', () => {
  let wrapper: any
  let mockServiceStore: any
  let mockNotificationStore: any

  beforeEach(async () => {
    vi.clearAllMocks()
    setActivePinia(createPinia())

    // Мок стора услуг
    mockServiceStore = {
      loading: false,
      error: null,
      categories: [
        { id: 1, name: 'СПА услуги', slug: 'spa' },
        { id: 2, name: 'Питание', slug: 'food' },
        { id: 3, name: 'Развлечения', slug: 'entertainment' }
      ],
      createService: vi.fn().mockResolvedValue(true),
      updateService: vi.fn().mockResolvedValue(true),
      getServiceById: vi.fn().mockReturnValue({
        id: 123,
        name: 'Массаж',
        description: 'Расслабляющий массаж',
        category: 'spa',
        price: 2000,
        duration: 60,
        is_free: false,
        requires_booking: true,
        is_active: true,
        max_persons: 1,
        time_slot_step: 30,
        image: '',
        card_width: 'half'
      }),
      fetchCategories: vi.fn().mockResolvedValue([])
    }

    // Мок стора уведомлений
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn()
    }

    // Настройка моков сторов
    const { useServiceStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useServiceStore).mockReturnValue(mockServiceStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(ServiceFormModal, {
      global: {
        stubs: {
          Teleport: true
        }
      }
    })
  })

  describe('Рендеринг формы', () => {
    it('отображает все основные поля', () => {
      expect(wrapper.find('input[placeholder="Введите название услуги"]').exists()).toBe(true)
      expect(wrapper.find('textarea[placeholder="Описание услуги"]').exists()).toBe(true)
      expect(wrapper.find('select').exists()).toBe(true)
      expect(wrapper.findAll('input[type="number"]').length).toBeGreaterThanOrEqual(3)
    })

    it('отображает поле для названия услуги', () => {
      const nameInput = wrapper.find('input[placeholder="Введите название услуги"]')
      expect(nameInput.exists()).toBe(true)
      expect(nameInput.attributes('required')).toBeDefined()
    })

    it('отображает поле для описания', () => {
      const descriptionInput = wrapper.find('textarea[placeholder="Описание услуги"]')
      expect(descriptionInput.exists()).toBe(true)
      expect(descriptionInput.attributes('rows')).toBe('3')
    })

    it('отображает селект категорий', () => {
      const categorySelect = wrapper.find('select')
      expect(categorySelect.exists()).toBe(true)
      expect(categorySelect.attributes('required')).toBeDefined()
      
      const options = categorySelect.findAll('option')
      expect(options.length).toBeGreaterThan(1)
      expect(wrapper.text()).toContain('СПА услуги')
      expect(wrapper.text()).toContain('Питание')
    })

    it('отображает поле для цены', () => {
      const priceInput = wrapper.findAll('input[type="number"]')[0]
      expect(priceInput.exists()).toBe(true)
      expect(priceInput.attributes('min')).toBe('0')
      expect(priceInput.attributes('step')).toBe('1')
    })

    it('отображает поле для продолжительности', () => {
      const durationInput = wrapper.findAll('input[type="number"]')[1]
      expect(durationInput.exists()).toBe(true)
      expect(durationInput.attributes('min')).toBe('1')
      expect(durationInput.attributes('required')).toBeDefined()
    })

    it('отображает чекбоксы опций', () => {
      const checkboxes = wrapper.findAll('input[type="checkbox"]')
      expect(checkboxes.length).toBe(3)
      
      expect(wrapper.text()).toContain('Бесплатная услуга')
      expect(wrapper.text()).toContain('Требует предварительного бронирования')
      expect(wrapper.text()).toContain('Активная услуга')
    })

    it('отображает поле для максимального количества человек', () => {
      const maxPersonsInput = wrapper.findAll('input[type="number"]')[2]
      expect(maxPersonsInput.exists()).toBe(true)
      expect(maxPersonsInput.attributes('min')).toBe('1')
      expect(maxPersonsInput.attributes('required')).toBeDefined()
    })

    it('отображает селект шага временного слота', () => {
      const timeSlotSelects = wrapper.findAll('select')
      const timeSlotSelect = timeSlotSelects.find((select: any) => {
        const options = select.findAll('option')
        return options.some((opt: any) => opt.text().includes('15 минут'))
      })
      
      expect(timeSlotSelect).toBeTruthy()
      if (timeSlotSelect) {
        const options = timeSlotSelect.findAll('option')
        expect(options.some((opt: any) => opt.text().includes('15 минут'))).toBe(true)
        expect(options.some((opt: any) => opt.text().includes('30 минут'))).toBe(true)
        expect(options.some((opt: any) => opt.text().includes('60 минут'))).toBe(true)
      }
    })

    it('отображает поле для URL изображения', () => {
      const imageInput = wrapper.find('input[type="url"]')
      expect(imageInput.exists()).toBe(true)
      expect(imageInput.attributes('placeholder')).toContain('https://')
    })

    it('отображает селект размера карточки', () => {
      const cardWidthSelects = wrapper.findAll('select')
      const cardWidthSelect = cardWidthSelects.find((select: any) => {
        const options = select.findAll('option')
        return options.some((opt: any) => opt.text().includes('Половина'))
      })
      
      expect(cardWidthSelect).toBeTruthy()
      if (cardWidthSelect) {
        const options = cardWidthSelect.findAll('option')
        expect(options.some((opt: any) => opt.text().includes('Половина'))).toBe(true)
        expect(options.some((opt: any) => opt.text().includes('Полная'))).toBe(true)
        expect(options.some((opt: any) => opt.text().includes('Треть'))).toBe(true)
      }
    })

    it('отображает кнопки действий', () => {
      const submitButton = wrapper.find('button[type="submit"]')
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      
      expect(submitButton.exists()).toBe(true)
      expect(cancelButton).toBeTruthy()
    })
  })

  describe('Валидация формы', () => {
    it('требует обязательные поля', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Проверяем, что форма не отправляется без обязательных полей
      expect(mockServiceStore.createService).not.toHaveBeenCalled()
    })

    it('валидирует название услуги', async () => {
      const nameInput = wrapper.find('input[placeholder="Введите название услуги"]')
      await nameInput.setValue('')
      
      expect(wrapper.vm.isFormValid).toBe(false)
    })

    it('валидирует выбор категории', async () => {
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = ''
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
    })

    it('валидирует продолжительность больше нуля', async () => {
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      wrapper.vm.form.duration = 0
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
    })

    it('валидирует максимальное количество человек больше нуля', async () => {
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      wrapper.vm.form.max_persons = 0
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
    })

    it('принимает валидную форму', async () => {
      await wrapper.find('input[placeholder="Введите назван��е услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      wrapper.vm.form.duration = 30
      wrapper.vm.form.max_persons = 1
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(true)
    })
  })

  describe('Функциональность бесплатной услуги', () => {
    it('обнуляет цену при выборе бесплатной услуги', async () => {
      const priceInput = wrapper.findAll('input[type="number"]')[0]
      await priceInput.setValue('1000')
      
      const freeCheckbox = wrapper.findAll('input[type="checkbox"]')[0]
      await freeCheckbox.setChecked(true)
      
      expect(wrapper.vm.form.price).toBe(0)
    })

    it('отключает поле цены для бесплатной услуги', async () => {
      const freeCheckbox = wrapper.findAll('input[type="checkbox"]')[0]
      await freeCheckbox.setChecked(true)
      
      await wrapper.vm.$nextTick()
      
      const priceInput = wrapper.findAll('input[type="number"]')[0]
      expect(priceInput.attributes('disabled')).toBeDefined()
    })
  })

  describe('Отправка формы', () => {
    it('создает новую услугу с правильными данными', async () => {
      // Заполняем об��зательные поля
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Новая услуга')
      await wrapper.find('textarea[placeholder="Описание услуги"]').setValue('Описание новой услуги')
      
      const categorySelect = wrapper.find('select')
      await categorySelect.setValue('spa')
      
      const priceInput = wrapper.findAll('input[type="number"]')[0]
      await priceInput.setValue('1500')
      
      const durationInput = wrapper.findAll('input[type="number"]')[1]
      await durationInput.setValue('45')
      
      const maxPersonsInput = wrapper.findAll('input[type="number"]')[2]
      await maxPersonsInput.setValue('2')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockServiceStore.createService).toHaveBeenCalledWith({
        name: 'Новая услуга',
        description: 'Описание новой услуги',
        category: 'spa',
        price: 1500,
        duration: 45,
        is_free: false,
        requires_booking: true,
        is_active: true,
        max_persons: 2,
        time_slot_step: 30,
        image: '',
        card_width: 'half'
      })
    })

    it('показывает уведомление об успе��е', async () => {
      // Заполняем валидную форму
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      await wrapper.vm.$nextTick()
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.success).toHaveBeenCalledWith('Успешно', 'Услуга создана')
    })

    it('закрывает модальное окно после успешного создания', async () => {
      // Заполняем валидную форму
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      await wrapper.vm.$nextTick()
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('close')).toBeTruthy()
    })
  })

  describe('Редактирование существующей услуги', () => {
    beforeEach(async () => {
      wrapper = mount(ServiceFormModal, {
        props: {
          serviceId: 123
        },
        global: {
          stubs: {
            Teleport: true
          }
        }
      })
      await wrapper.vm.$nextTick()
    })

    it('загружает данные существующей услуги', () => {
      expect(mockServiceStore.getServiceById).toHaveBeenCalledWith(123)
    })

    it('заполняет форму данными услуги', async () => {
      await wrapper.vm.$nextTick()
      
      const nameInput = wrapper.find('input[placeholder="Введите название услуги"]')
      const descriptionInput = wrapper.find('textarea[placeholder="Описание услуги"]')
      const categorySelect = wrapper.find('select')
      
      expect(nameInput.element.value).toBe('Массаж')
      expect(descriptionInput.element.value).toBe('Расслабляющий массаж')
      expect(categorySelect.element.value).toBe('spa')
      expect(wrapper.vm.form.price).toBe(2000)
      expect(wrapper.vm.form.duration).toBe(60)
    })

    it('обновляет существующую услугу', async () => {
      await wrapper.vm.$nextTick()
      
      // Изменяем данные
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Обновленный массаж')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockServiceStore.updateService).toHaveBeenCalledWith(123, expect.objectContaining({
        name: 'Обновленный массаж'
      }))
    })
  })

  describe('Обработка ошибок', () => {
    it('показывает ошибку при неудачном создании', async () => {
      mockServiceStore.createService.mockResolvedValue(false)
      
      // Заполняем валидную форму
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      await wrapper.vm.$nextTick()
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.error).toHaveBeenCalledWith('Ошибка', 'Не удалось создать услугу')
    })

    it('показывает ошибку при исключении', async () => {
      mockServiceStore.createService.mockRejectedValue(new Error('Ошибка сервера'))
      
      // Заполняем валидную форму
      await wrapper.find('input[placeholder="Введите название услуги"]').setValue('Тест услуга')
      wrapper.vm.form.category = 'spa'
      await wrapper.vm.$nextTick()
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.error).toHaveBeenCalledWith('Ошибка', 'Произошла ошибка при сохранении услуги')
    })

    it('отображает состояние загрузки', async () => {
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
      expect(submitButton.text()).toContain('Создание...')
    })
  })

  describe('Закрытие модального окна', () => {
    it('закрывается при клике на кнопку отмены', async () => {
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      if (cancelButton) {
        await cancelButton.trigger('click')
        expect(wrapper.emitted('close')).toBeTruthy()
      }
    })
  })

  describe('Инициализация компонента', () => {
    it('инициализирует пустую форму для новой услуги', () => {
      expect(wrapper.vm.form.name).toBe('')
      expect(wrapper.vm.form.description).toBe('')
      expect(wrapper.vm.form.category).toBe('')
      expect(wrapper.vm.form.price).toBe(0)
      expect(wrapper.vm.form.duration).toBe(30)
      expect(wrapper.vm.form.is_free).toBe(false)
      expect(wrapper.vm.form.requires_booking).toBe(true)
      expect(wrapper.vm.form.is_active).toBe(true)
      expect(wrapper.vm.form.max_persons).toBe(1)
      expect(wrapper.vm.form.time_slot_step).toBe(30)
      expect(wrapper.vm.form.image).toBe('')
      expect(wrapper.vm.form.card_width).toBe('half')
    })

    it('загружает категории при отсутствии данных', async () => {
      mockServiceStore.categories = []
      
      wrapper = mount(ServiceFormModal, {
        global: {
          stubs: {
            Teleport: true
          }
        }
      })
      
      await wrapper.vm.$nextTick()
      
      expect(mockServiceStore.fetchCategories).toHaveBeenCalled()
    })
  })

  describe('Вычисляемые свойства', () => {
    it('правильно определяет режим редактирования', async () => {
      expect(wrapper.vm.isEditing).toBe(false)
      
      wrapper = mount(ServiceFormModal, {
        props: {
          serviceId: 123
        },
        global: {
          stubs: {
            Teleport: true
          }
        }
      })
      
      expect(wrapper.vm.isEditing).toBe(true)
    })

    it('правильно валидирует форму', async () => {
      // Пустая форма невалидна
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // Заполняем обязательные поля
      wrapper.vm.form.name = 'Тест услуга'
      wrapper.vm.form.category = 'spa'
      wrapper.vm.form.duration = 30
      wrapper.vm.form.max_persons = 1
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(true)
    })
  })

  describe('Адаптивность', () => {
    it('корректно отображается на мобильных устройствах', async () => {
      // Симуляция мобильного экрана
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      await wrapper.vm.$nextTick()
      
      // Проверяем адаптивные классы
      const gridElements = wrapper.findAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })
})