import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StaffFormModal from '@/components/modals/StaffFormModal.vue'

// Мокаем сторы
vi.mock('@/stores', () => ({
  useStaffStore: vi.fn(),
  useNotificationStore: vi.fn()
}))

describe('StaffFormModal.vue', () => {
  let wrapper: any
  let mockStaffStore: any
  let mockNotificationStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок стора сотрудников
    mockStaffStore = {
      loading: false,
      error: null,
      staff: [
        {
          id: 1,
          name: 'Иван Петров',
          position: 'Массажист',
          department: 'SPA',
          phone: '+7 (999) 123-45-67',
          email: '<EMAIL>',
          specializations: ['Класси��еский массаж', 'Спортивный массаж'],
          is_active: true,
          working_hours: {
            monday: { is_working: true, start: '09:00', end: '18:00' },
            tuesday: { is_working: true, start: '09:00', end: '18:00' },
            wednesday: { is_working: true, start: '09:00', end: '18:00' },
            thursday: { is_working: true, start: '09:00', end: '18:00' },
            friday: { is_working: true, start: '09:00', end: '18:00' },
            saturday: { is_working: false, start: '10:00', end: '17:00' },
            sunday: { is_working: false, start: '10:00', end: '17:00' }
          }
        }
      ],
      createStaff: vi.fn().mockResolvedValue(true),
      updateStaff: vi.fn().mockResolvedValue(true),
      getStaffById: vi.fn().mockReturnValue({
        id: 1,
        name: 'Иван Петров',
        position: 'Массажист',
        department: 'SPA',
        phone: '+7 (999) 123-45-67',
        email: '<EMAIL>',
        specializations: ['Классический массаж'],
        is_active: true,
        working_hours: {
          monday: { is_working: true, start: '09:00', end: '18:00' },
          tuesday: { is_working: true, start: '09:00', end: '18:00' },
          wednesday: { is_working: true, start: '09:00', end: '18:00' },
          thursday: { is_working: true, start: '09:00', end: '18:00' },
          friday: { is_working: true, start: '09:00', end: '18:00' },
          saturday: { is_working: false, start: '10:00', end: '17:00' },
          sunday: { is_working: false, start: '10:00', end: '17:00' }
        }
      })
    }

    // Мок стора уведомлений
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn()
    }

    // Настройка моков сторов
    const { useStaffStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(StaffFormModal, {
      global: {
        stubs: {
          Teleport: true
        }
      }
    })
  })

  describe('Рендеринг формы', () => {
    it('отображает все основные поля', () => {
      expect(wrapper.find('input[placeholder="Введите имя сотрудника"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').exists()).toBe(true)
      expect(wrapper.find('select').exists()).toBe(true)
      expect(wrapper.find('input[type="tel"]').exists()).toBe(true)
      expect(wrapper.find('input[type="email"]').exists()).toBe(true)
    })

    it('отображает поле для имени сотрудника', () => {
      const nameInput = wrapper.find('input[placeholder="Введите имя сотрудника"]')
      expect(nameInput.exists()).toBe(true)
      expect(nameInput.attributes('required')).toBeDefined()
    })

    it('отображает селект отделов', () => {
      const departmentSelect = wrapper.find('select')
      expect(departmentSelect.exists()).toBe(true)
      
      const options = departmentSelect.findAll('option')
      expect(options.length).toBeGreaterThan(1)
      
      const optionTexts = options.map((option: any) => option.text())
      expect(optionTexts).toContain('SPA')
      expect(optionTexts).toContain('Ресторан')
      expect(optionTexts).toContain('Ресепшн')
    })

    it('отображает поле для должности', () => {
      const positionInput = wrapper.find('input[placeholder="Массажист, Администратор и т.д."]')
      expect(positionInput.exists()).toBe(true)
      expect(positionInput.attributes('required')).toBeDefined()
    })

    it('отображает поле для телефона', () => {
      const phoneInput = wrapper.find('input[type="tel"]')
      expect(phoneInput.exists()).toBe(true)
      expect(phoneInput.attributes('required')).toBeDefined()
    })

    it('отображает поле для email', () => {
      const emailInput = wrapper.find('input[type="email"]')
      expect(emailInput.exists()).toBe(true)
      expect(emailInput.attributes('required')).toBeDefined()
    })

    it('отображает кнопки действий', () => {
      const submitButton = wrapper.find('button[type="submit"]')
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      
      expect(submitButton.exists()).toBe(true)
      expect(cancelButton).toBeTruthy()
    })

    it('отображает секцию специализаций', () => {
      const specializationInput = wrapper.find('input[placeholder="Добавить специализацию"]')
      const addButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Добавить'))
      
      expect(specializationInput.exists()).toBe(true)
      expect(addButton).toBeTruthy()
    })
  })

  describe('Валидация формы', () => {
    it('требует обязательные поля', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Проверяем, что форма не отправляется б��з обязательных полей
      expect(mockStaffStore.createStaff).not.toHaveBeenCalled()
    })

    it('валидирует имя сотрудника', async () => {
      const nameInput = wrapper.find('input[placeholder="Введите имя сотрудника"]')
      await nameInput.setValue('')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.createStaff).not.toHaveBeenCalled()
    })

    it('валидирует выбор отдела', async () => {
      const departmentSelect = wrapper.find('select')
      await departmentSelect.setValue('')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.createStaff).not.toHaveBeenCalled()
    })

    it('валидирует формат email', async () => {
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Тест Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Тестер')
      await wrapper.find('select').setValue('SPA')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('invalid-email')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.createStaff).not.toHaveBeenCalled()
    })

    it('валидирует корректный email', async () => {
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Тест Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Тестер')
      await wrapper.find('select').setValue('SPA')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('<EMAIL>')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.createStaff).toHaveBeenCalled()
    })
  })

  describe('Работа со специализациями', () => {
    it('позволяет добавлять специализации', async () => {
      const specializationInput = wrapper.find('input[placeholder="Добавить специализацию"]')
      const addButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Добавить'))
      
      await specializationInput.setValue('Новая специализация')
      await addButton.trigger('click')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.form.specializations).toContain('Новая специализация')
    })

    it('позволяет удалять специализации', async () => {
      // Сначала добавляем специализацию
      const specializationInput = wrapper.find('input[placeholder="Добавить специализацию"]')
      const addButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Добавить'))
      
      await specializationInput.setValue('Тестовая специализация')
      await addButton.trigger('click')
      
      await wrapper.vm.$nextTick()
      
      // Теперь удаляем её
      const removeButton = wrapper.find('button:contains("×")')
      if (removeButton.exists()) {
        await removeButton.trigger('click')
        await wrapper.vm.$nextTick()
        expect(wrapper.vm.form.specializations).not.toContain('Тестовая специализация')
      }
    })

    it('валидирует непустые специализации', async () => {
      const specializationInput = wrapper.find('input[placeholder="Добавить специализацию"]')
      const addButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Добави��ь'))
      
      await specializationInput.setValue('   ')
      await addButton.trigger('click')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.form.specializations.length).toBe(0)
    })
  })

  describe('Работа с расписанием', () => {
    it('отображает настройки расписания', () => {
      const workingHoursSection = wrapper.findAll('input[type="checkbox"]')
      expect(workingHoursSection.length).toBeGreaterThan(0)
    })

    it('позволяет настраивать рабочие часы', async () => {
      const timeInputs = wrapper.findAll('input[type="time"]')
      expect(timeInputs.length).toBeGreaterThan(0)
      
      if (timeInputs.length > 0) {
        await timeInputs[0].setValue('10:00')
        expect(timeInputs[0].element.value).toBe('10:00')
      }
    })

    it('позволяет включать/выключать рабочие дни', async () => {
      const checkboxes = wrapper.findAll('input[type="checkbox"]')
      const workingDayCheckbox = checkboxes.find((cb: any) => 
        cb.element.nextElementSibling?.textContent?.includes('Рабочий день')
      )
      
      if (workingDayCheckbox) {
        const initialValue = workingDayCheckbox.element.checked
        await workingDayCheckbox.setChecked(!initialValue)
        expect(workingDayCheckbox.element.checked).toBe(!initialValue)
      }
    })
  })

  describe('Отправка формы', () => {
    it('создает нового сотрудника', async () => {
      // Заполняем обязательные поля
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Новый Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Администратор')
      await wrapper.find('select').setValue('Reception')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('<EMAIL>')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.createStaff).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Новый Сотрудник',
          position: 'Администратор',
          department: 'Reception',
          phone: '****** 123 45 67',
          email: '<EMAIL>'
        })
      )
    })

    it('показывает уведомление об успехе', async () => {
      // Заполняем форму и отправляем
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Тест Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Тестер')
      await wrapper.find('select').setValue('SPA')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('<EMAIL>')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Ждем завершения асинхронной операции
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.success).toHaveBeenCalledWith('Успешно', 'Сотрудник создан')
    })

    it('закрывает модальное окно после успешного создания', async () => {
      const closeSpy = vi.fn()
      wrapper.vm.$emit = closeSpy
      
      // Заполняем форму и отправляем
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Тест Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Тестер')
      await wrapper.find('select').setValue('SPA')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('<EMAIL>')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      // Ждем завершения асинхронной операции
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('close')).toBeTruthy()
    })
  })

  describe('Редактирование существующего сотрудника', () => {
    beforeEach(async () => {
      wrapper = mount(StaffFormModal, {
        props: {
          staffId: 1
        },
        global: {
          stubs: {
            Teleport: true
          }
        }
      })
      await wrapper.vm.$nextTick()
    })

    it('загружает данные существующего сотрудника', () => {
      expect(mockStaffStore.getStaffById).toHaveBeenCalledWith(1)
    })

    it('заполняет форму данными сотрудника', async () => {
      await wrapper.vm.$nextTick()
      
      const nameInput = wrapper.find('input[placeholder="Введите имя сотрудника"]')
      const positionInput = wrapper.find('input[placeholder="Массажист, Администратор и т.д."]')
      const departmentSelect = wrapper.find('select')
      const emailInput = wrapper.find('input[type="email"]')
      
      expect(nameInput.element.value).toBe('Иван Петров')
      expect(positionInput.element.value).toBe('Массажист')
      expect(departmentSelect.element.value).toBe('SPA')
      expect(emailInput.element.value).toBe('<EMAIL>')
    })

    it('обновляет существующего сотрудника', async () => {
      await wrapper.vm.$nextTick()
      
      // Изменяем данные
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Иван Обновленный')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      expect(mockStaffStore.updateStaff).toHaveBeenCalledWith(1, expect.objectContaining({
        name: 'Иван Обновленный'
      }))
    })
  })

  describe('Обработка ошибок', () => {
    it('показывает ошибку при неудачном создании', async () => {
      mockStaffStore.createStaff.mockResolvedValue(false)
      
      // Заполняем и отправляем форму
      await wrapper.find('input[placeholder="Введите имя сотрудника"]').setValue('Тест Сотрудник')
      await wrapper.find('input[placeholder="Массажист, Администратор и т.д."]').setValue('Тестер')
      await wrapper.find('select').setValue('SPA')
      await wrapper.find('input[type="tel"]').setValue('****** 123 45 67')
      await wrapper.find('input[type="email"]').setValue('<EMAIL>')
      
      const form = wrapper.find('form')
      await form.trigger('submit')
      
      await wrapper.vm.$nextTick()
      
      expect(mockNotificationStore.error).toHaveBeenCalledWith('Ошибка', 'Не удалось создать сотрудника')
    })

    it('отображает состояние загрузки', async () => {
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
      expect(submitButton.text()).toContain('Создание...')
    })
  })

  describe('Закрытие модального окна', () => {
    it('закрывается при клике на кнопку отмены', async () => {
      const cancelButton = wrapper.findAll('button').find((btn: any) => btn.text().includes('Отмена'))
      if (cancelButton) {
        await cancelButton.trigger('click')
        expect(wrapper.emitted('close')).toBeTruthy()
      }
    })
  })

  describe('Инициализация компонента', () => {
    it('инициализирует пустую форм�� для нового сотрудника', () => {
      expect(wrapper.vm.form.name).toBe('')
      expect(wrapper.vm.form.position).toBe('')
      expect(wrapper.vm.form.department).toBe('')
      expect(wrapper.vm.form.phone).toBe('')
      expect(wrapper.vm.form.email).toBe('')
      expect(wrapper.vm.form.is_active).toBe(true)
    })

    it('инициализирует расписание по умолчанию', () => {
      expect(wrapper.vm.form.working_hours.monday.is_working).toBe(true)
      expect(wrapper.vm.form.working_hours.monday.start).toBe('09:00')
      expect(wrapper.vm.form.working_hours.monday.end).toBe('18:00')
    })
  })

  describe('Адаптивность', () => {
    it('корректно отображается на мобильных устройствах', async () => {
      // Симуляция мобильного экрана
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      await wrapper.vm.$nextTick()
      
      // Проверяем адаптивные классы
      const gridElements = wrapper.findAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })

  describe('Доступность', () => {
    it('имеет правильные лейблы для п��лей', () => {
      const labels = wrapper.findAll('label')
      expect(labels.length).toBeGreaterThan(0)
      
      const labelTexts = labels.map((label: any) => label.text())
      expect(labelTexts).toContain('Имя сотрудника *')
      expect(labelTexts).toContain('Должность *')
      expect(labelTexts).toContain('Отдел *')
    })
  })
})