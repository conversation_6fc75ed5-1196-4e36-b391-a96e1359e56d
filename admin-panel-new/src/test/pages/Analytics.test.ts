import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Analytics from '@/pages/Analytics.vue'

// Мокаем сторы
vi.mock('@/stores', () => ({
  useAnalyticsStore: vi.fn(),
  useBookingStore: vi.fn(),
  useServiceStore: vi.fn(),
  useStaffStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))// Мокаем Chart.js компоненты
vi.mock('vue-chartjs', () => ({
  Line: { name: '<PERSON><PERSON><PERSON>' },
  Bar: { name: '<PERSON><PERSON><PERSON>' },
  Doughnut: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  Pie: { name: '<PERSON><PERSON><PERSON>' }
}))

// Мокаем иконки
vi.mock('~icons/lucide/trending-up', () => ({ default: { name: 'TrendingUpIcon' } }))
vi.mock('~icons/lucide/trending-down', () => ({ default: { name: 'TrendingDownIcon' } }))
vi.mock('~icons/lucide/calendar', () => ({ default: { name: 'CalendarIcon' } }))
vi.mock('~icons/lucide/download', () => ({ default: { name: 'DownloadIcon' } }))
vi.mock('~icons/lucide/refresh-cw', () => ({ default: { name: 'RefreshIcon' } }))
vi.mock('~icons/lucide/filter', () => ({ default: { name: 'FilterIcon' } }))

describe('Analytics.vue', () => {
  let wrapper: any
  let mockAnalyticsStore: any
  let mockBookingStore: any
  let mockServiceStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок стора аналитики
    mockAnalyticsStore = {
      loading: false,
      error: null,
      analytics: {
        totalBookings: 1250,
        totalRevenue: 375000,
        averageBookingValue: 300,
        cancellationRate: 4.8,
        occupancyRate: 78.5,
        customerSatisfaction: 4.6,
        popularServices: [
          { name: 'Массаж', count: 145, revenue: 43500, percentage: 11.6 },
          { name: 'СПА процедуры', count: 98, revenue: 29400, percentage: 7.8 },
          { name: 'Завтрак в номер', count: 234, revenue: 35100, percentage: 18.7 }
        ],
        revenueByCategory: [
          { category: 'spa', name: 'СПА услуги', revenue: 180000, count: 600, percentage: 48 },
          { category: 'food', name: 'Питание', revenue: 120000, count: 400, percentage: 32 },
          { category: 'room', name: 'Номера', revenue: 75000, count: 250, percentage: 20 }
        ],
        bookingsByStatus: [
          { status: 'confirmed', name: 'Подтверждено', count: 1000, percentage: 80 },
          { status: 'pending', name: 'Ожидает', count: 150, percentage: 12 },
          { status: 'cancelled', name: 'Отменено', count: 100, percentage: 8 }
        ],
        dailyStats: [
          { date: '2024-01-01', bookings: 45, revenue: 13500, cancellations: 2 },
          { date: '2024-01-02', bookings: 52, revenue: 15600, cancellations: 1 },
          { date: '2024-01-03', bookings: 38, revenue: 11400, cancellations: 3 }
        ],
        monthlyStats: [
          { month: '2024-01', bookings: 1200, revenue: 360000, cancellations: 60 },
          { month: '2024-02', bookings: 1100, revenue: 330000, cancellations: 55 },
          { month: '2024-03', bookings: 1350, revenue: 405000, cancellations: 65 }
        ],
        hourlyDistribution: [
          { hour: 9, bookings: 45 },
          { hour: 10, bookings: 78 },
          { hour: 11, bookings: 92 },
          { hour: 12, bookings: 85 },
          { hour: 13, bookings: 67 },
          { hour: 14, bookings: 89 },
          { hour: 15, bookings: 95 },
          { hour: 16, bookings: 82 },
          { hour: 17, bookings: 71 },
          { hour: 18, bookings: 58 }
        ],
        customerMetrics: {
          newCustomers: 245,
          returningCustomers: 1005,
          averageBookingsPerCustomer: 2.3,
          customerLifetimeValue: 890
        }
      },
      dateRange: {
        start: '2024-01-01',
        end: '2024-03-31'
      },
      fetchAnalytics: vi.fn(),
      updateDateRange: vi.fn(),
      exportReport: vi.fn(),
      refreshData: vi.fn()
    }

    // Мок стора бронирований
    mockBookingStore = {
      bookings: [],
      fetchBookings: vi.fn()
    }

    // Мок стора услуг
    mockServiceStore = {
      services: [],
      categories: [],
      fetchServices: vi.fn()
    }

    // Настройка моков сторов
    const { useAnalyticsStore, useBookingStore, useServiceStore } = await import('@/stores')
    vi.mocked(useAnalyticsStore).mockReturnValue(mockAnalyticsStore)
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useServiceStore).mockReturnValue(mockServiceStore)

    wrapper = mount(Analytics, {
      global: {
        stubs: {
          LineChart: true,
          BarChart: true,
          DoughnutChart: true,
          PieChart: true,
          TrendingUpIcon: true,
          TrendingDownIcon: true,
          CalendarIcon: true,
          DownloadIcon: true,
          RefreshIcon: true,
          FilterIcon: true
        }
      }
    })
  })

  describe('Рендеринг компонента', () => {
    it('отображает заголовок страницы', () => {
      expect(wrapper.find('h1').text()).toBe('Аналитика и отчеты')
      expect(wrapper.find('p').text()).toBe('Детальная аналитика по бронированиям и доходам')
    })

    it('отображает элементы управления', () => {
      const refreshButton = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => btn.text().includes('Обновить'))
      const exportButton = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => btn.text().includes('Экспорт'))
      
      expect(refreshButton).toBeTruthy()
      expect(exportButton).toBeTruthy()
    })

    it('отображает фильтры по датам', () => {
      const dateInputs = wrapper.findAll('input[type="date"]')
      expect(dateInputs.length).toBeGreaterThanOrEqual(2) // Начальная и конечная дата
    })

    it('отображает основные метрики', () => {
      expect(wrapper.find('[data-testid="total-bookings"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="total-revenue"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="average-booking-value"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="cancellation-rate"]').exists()).toBe(true)
    })
  })

  describe('Отображение метрик', () => {
    it('отображает общее количество бронирований', () => {
      const totalBookings = wrapper.find('[data-testid="total-bookings"]')
      expect(totalBookings.text()).toContain('1250')
    })

    it('отображает общий доход', () => {
      const totalRevenue = wrapper.find('[data-testid="total-revenue"]')
      expect(totalRevenue.text()).toContain('375000')
    })

    it('отображает средний чек', () => {
      const avgBookingValue = wrapper.find('[data-testid="average-booking-value"]')
      expect(avgBookingValue.text()).toContain('300')
    })

    it('отображает процент отмен', () => {
      const cancellationRate = wrapper.find('[data-testid="cancellation-rate"]')
      expect(cancellationRate.text()).toContain('4.8')
    })

    it('отображает заполняемость', () => {
      const occupancyRate = wrapper.find('[data-testid="occupancy-rate"]')
      if (occupancyRate.exists()) {
        expect(occupancyRate.text()).toContain('78.5')
      }
    })

    it('отображает удовлетворенность клиентов', () => {
      const satisfaction = wrapper.find('[data-testid="customer-satisfaction"]')
      if (satisfaction.exists()) {
        expect(satisfaction.text()).toContain('4.6')
      }
    })
  })

  describe('Графики и диаграммы', () => {
    it('отображает график доходов по времени', () => {
      const revenueChart = wrapper.find('[data-testid="revenue-chart"]')
      expect(revenueChart.exists()).toBe(true)
    })

    it('отображает диаграмму популярных услуг', () => {
      const servicesChart = wrapper.find('[data-testid="popular-services-chart"]')
      expect(servicesChart.exists()).toBe(true)
    })

    it('отображает диаграмму доходов по категориям', () => {
      const categoriesChart = wrapper.find('[data-testid="revenue-by-category-chart"]')
      expect(categoriesChart.exists()).toBe(true)
    })

    it('отображает диаграмму статусов бронирований', () => {
      const statusChart = wrapper.find('[data-testid="bookings-status-chart"]')
      expect(statusChart.exists()).toBe(true)
    })

    it('отображает график почасового распределения', () => {
      const hourlyChart = wrapper.find('[data-testid="hourly-distribution-chart"]')
      if (hourlyChart.exists()) {
        expect(hourlyChart.exists()).toBe(true)
      }
    })
  })

  describe('Популярные услуги', () => {
    it('отображает список популярных услуг', () => {
      const servicesList = wrapper.find('[data-testid="popular-services-list"]')
      expect(servicesList.exists()).toBe(true)
    })

    it('отображает данные о каждой популярной услуге', () => {
      const serviceItems = wrapper.findAll('[data-testid^="service-item-"]')
      expect(serviceItems.length).toBeGreaterThan(0)
      
      if (serviceItems.length > 0) {
        expect(serviceItems[0].text()).toContain('Массаж')
        expect(serviceItems[0].text()).toContain('145')
        expect(serviceItems[0].text()).toContain('43500')
      }
    })
  })

  describe('Функциональность фильтров', () => {
    it('обновляет диапазон дат', async () => {
      const startDateInput = wrapper.find('input[data-testid="start-date"]')
      const endDateInput = wrapper.find('input[data-testid="end-date"]')
      
      if (startDateInput.exists() && endDateInput.exists()) {
        await startDateInput.setValue('2024-01-01')
        await endDateInput.setValue('2024-01-31')
        
        expect(mockAnalyticsStore.updateDateRange).toHaveBeenCalledWith({
          start: '2024-01-01',
          end: '2024-01-31'
        })
      }
    })

    it('применяет фильтры', async () => {
      const applyButton = wrapper.find('[data-testid="apply-filters"]')
      if (applyButton.exists()) {
        await applyButton.trigger('click')
        expect(mockAnalyticsStore.fetchAnalytics).toHaveBeenCalled()
      }
    })

    it('сбрасывает фильтры', async () => {
      const resetButton = wrapper.find('[data-testid="reset-filters"]')
      if (resetButton.exists()) {
        await resetButton.trigger('click')
        // Проверяем, что фильтры сброшены
        expect(wrapper.vm.dateRange.start).toBe('')
        expect(wrapper.vm.dateRange.end).toBe('')
      }
    })
  })

  describe('Действия', () => {
    it('обновляет данные', async () => {
      const refreshButton = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => btn.text().includes('Обновить'))
      await refreshButton.trigger('click')
      
      expect(mockAnalyticsStore.refreshData).toHaveBeenCalled()
    })

    it('экспортирует отчет', async () => {
      const exportButton = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => btn.text().includes('Экспорт'))
      await exportButton.trigger('click')
      
      expect(mockAnalyticsStore.exportReport).toHaveBeenCalled()
    })
  })

  describe('Состояния загрузки', () => {
    it('отображает индикатор загрузки', async () => {
      mockAnalyticsStore.loading = true
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    })

    it('отображает сообщение об ошибке', async () => {
      mockAnalyticsStore.error = 'Ошибка загрузки аналитики'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Ошибка загрузки аналитики')
    })
  })

  describe('Метрики клиентов', () => {
    it('отображает количество новых клиентов', () => {
      const newCustomers = wrapper.find('[data-testid="new-customers"]')
      if (newCustomers.exists()) {
        expect(newCustomers.text()).toContain('245')
      }
    })

    it('отображает количество возвращающихся клиентов', () => {
      const returningCustomers = wrapper.find('[data-testid="returning-customers"]')
      if (returningCustomers.exists()) {
        expect(returningCustomers.text()).toContain('1005')
      }
    })

    it('отображает среднее количество бронирований на клиента', () => {
      const avgBookings = wrapper.find('[data-testid="avg-bookings-per-customer"]')
      if (avgBookings.exists()) {
        expect(avgBookings.text()).toContain('2.3')
      }
    })

    it('отображает пожизненную ценность клиента', () => {
      const ltv = wrapper.find('[data-testid="customer-lifetime-value"]')
      if (ltv.exists()) {
        expect(ltv.text()).toContain('890')
      }
    })
  })

  describe('Тренды и сравнения', () => {
    it('отображает тренды роста', () => {
      const trendIndicators = wrapper.findAll('[data-testid^="trend-"]')
      expect(trendIndicators.length).toBeGreaterThan(0)
    })

    it('показывает сравнение с предыдущим периодом', () => {
      const comparison = wrapper.find('[data-testid="period-comparison"]')
      if (comparison.exists()) {
        expect(comparison.exists()).toBe(true)
      }
    })
  })

  describe('Инициализация компонента', () => {
    it('загружает данные при монтировании', () => {
      expect(mockAnalyticsStore.fetchAnalytics).toHaveBeenCalled()
    })
  })

  describe('Адаптивность', () => {
    it('корректно отображается на мобильных устройствах', async () => {
      // Симуляция мобильного экрана
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      await wrapper.vm.$nextTick()
      
      // Проверяем адаптивные классы
      const metricsGrid = wrapper.find('[data-testid="metrics-grid"]')
      if (metricsGrid.exists()) {
        expect(metricsGrid.classes()).toContain('grid')
      }
    })

    it('адаптирует графики под размер экрана', async () => {
      const charts = wrapper.findAll('[data-testid$="-chart"]')
      expect(charts.length).toBeGreaterThan(0)
    })
  })

  describe('Форматирование данных', () => {
    it('форматирует валютные значения', () => {
      const revenueElements = wrapper.findAll('[data-testid*="revenue"]')
      if (revenueElements.length > 0) {
        // Проверяем, что значения отформатированы как валюта
        expect(revenueElements[0].text()).toMatch(/\d+/)
      }
    })

    it('форматирует проценты', () => {
      const percentageElements = wrapper.findAll('[data-testid*="rate"], [data-testid*="percentage"]')
      if (percentageElements.length > 0) {
        expect(percentageElements[0].text()).toMatch(/%/)
      }
    })
  })
})