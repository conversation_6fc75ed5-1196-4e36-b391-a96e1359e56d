import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Dashboard from '@/pages/Dashboard.vue'

// Mock the stores
vi.mock('@/stores', () => ({
  useBookingStore: vi.fn(),
  useAnalyticsStore: vi.fn(),
  useServiceStore: vi.fn(),
  useStaffStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))

describe('Dashboard.vue', () => {
  let wrapper: any
  let mockBookingStore: any
  let mockAnalyticsStore: any
  let mockServiceStore: any
  let mockStaffStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Mock booking store
    mockBookingStore = {
      loading: false,
      bookings: [],
      fetchBookings: vi.fn(),
      filteredBookings: [],
    }

    // Mock analytics store
    mockAnalyticsStore = {
      loading: false,
      analytics: {
        totalBookings: 150,
        totalRevenue: 45000,
        averageBookingValue: 300,
        cancellationRate: 5.2,
        popularServices: [
          { name: 'Масса<PERSON>', count: 45, revenue: 13500 },
          { name: 'СПА процедуры', count: 32, revenue: 9600 }
        ],
        revenueByCategory: [
          { category: 'spa', revenue: 25000, count: 80 },
          { category: 'food', revenue: 15000, count: 50 }
        ],
        bookingsByStatus: [
          { status: 'confirmed', count: 120, percentage: 80 },
          { status: 'pending', count: 20, percentage: 13.3 },
          { status: 'cancelled', count: 10, percentage: 6.7 }
        ],
        dailyStats: [
          { date: '2024-01-01', bookings: 15, revenue: 4500, cancellations: 1 },
          { date: '2024-01-02', bookings: 18, revenue: 5400, cancellations: 0 }
        ]
      },
      fetchAnalytics: vi.fn(),
    }

    // Mock service store
    mockServiceStore = {
      loading: false,
      services: [
        { id: 1, name: 'Массаж', category: 'spa', price: 300 },
        { id: 2, name: 'Завтрак', category: 'food', price: 150 }
      ],
      fetchServices: vi.fn(),
    }

    // Mock staff store
    mockStaffStore = {
      loading: false,
      staff: [
        { id: 1, name: 'Иван Иванов', position: 'Массажист' },
        { id: 2, name: 'Мария Петрова', position: 'Администратор' }
      ],
      fetchStaff: vi.fn(),
    }

    // Setup store mocks
    const { useBookingStore, useAnalyticsStore, useServiceStore, useStaffStore } = await import('@/stores')
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useAnalyticsStore).mockReturnValue(mockAnalyticsStore)
    vi.mocked(useServiceStore).mockReturnValue(mockServiceStore)
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)

    wrapper = mount(Dashboard, {
      global: {
        stubs: {
          // Stub chart components that might be complex to test
          'Line': true,
          'Bar': true,
          'Doughnut': true,
          'RouterLink': true,
          'CalendarIcon': true,
          'CurrencyIcon': true,
          'UsersIcon': true,
          'ChartIcon': true,
          'ServicesIcon': true,
          'DocumentIcon': true,
        }
      }
    })
  })

  describe('Component Rendering', () => {
    it('renders the dashboard header correctly', () => {
      expect(wrapper.find('h1').text()).toBe('Добро пожаловать в админ-панель')
    })

    it('успешно рендерится', () => {
      expect(wrapper.exists()).toBe(true);
    });

    it('renders key metrics cards', () => {
      const cards = wrapper.findAll('.bg-white')
      expect(cards.length).toBeGreaterThan(0)
    })

    it('displays total bookings metric', () => {
      expect(wrapper.text()).toContain('150') // Total bookings
    })

    it('displays total revenue metric', () => {
      expect(wrapper.text()).toContain('45,000') // Total revenue
    })

    it('displays average booking value', () => {
      expect(wrapper.text()).toContain('300') // Average booking value
    })

    it('displays cancellation rate', () => {
      expect(wrapper.text()).toContain('5.2%') // Cancellation rate
    })
  })

  describe('Analytics Data Display', () => {
    it('shows popular services', () => {
      expect(wrapper.text()).toContain('Массаж')
      expect(wrapper.text()).toContain('СПА процедуры')
    })

    it('displays service counts and revenue', () => {
      expect(wrapper.text()).toContain('45') // Massage count
      expect(wrapper.text()).toContain('13,500') // Massage revenue
    })

    it('shows booking status distribution', () => {
      expect(wrapper.text()).toContain('120') // Confirmed bookings
      expect(wrapper.text()).toContain('80%') // Confirmed percentage
    })

    it('displays revenue by category', () => {
      expect(wrapper.text()).toContain('25,000') // SPA revenue
      expect(wrapper.text()).toContain('15,000') // Food revenue
    })
  })

  describe('Recent Activity', () => {
    it('shows recent bookings section', () => {
      expect(wrapper.text()).toContain('Последние бронирования')
    })

    it('displays booking information when bookings exist', async () => {
      mockBookingStore.filteredBookings = [
        {
          id: 1,
          guest_name: 'Тест Гость',
          services: [{ service_name: 'Массаж' }],
          date: '2024-01-15',
          time: '10:00',
          status: 'confirmed'
        }
      ]

      await wrapper.vm.$nextTick()
      expect(wrapper.text()).toContain('Тест Гость')
    })

    it('shows empty state when no recent bookings', () => {
      mockBookingStore.filteredBookings = []
      expect(wrapper.text()).toContain('Нет недавних бронирований')
    })
  })

  describe('Quick Actions', () => {
    it('renders quick action buttons', () => {
      const buttons = wrapper.findAll('button')
      expect(buttons.length).toBeGreaterThan(0)
    })

    it('has new booking button', () => {
      const newBookingBtn = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => 
        btn.text().includes('Новое бронирование')
      )
      expect(newBookingBtn).toBeTruthy()
    })

    it('has view calendar button', () => {
      const calendarBtn = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => 
        btn.text().includes('Календарь')
      )
      expect(calendarBtn).toBeTruthy()
    })

    it('has reports button', () => {
      const reportsBtn = wrapper.findAll('button').find((btn: { text: () => string | string[] }) => 
        btn.text().includes('Отчеты')
      )
      expect(reportsBtn).toBeTruthy()
    })
  })

  describe('Loading States', () => {
    it('shows loading state for analytics', async () => {
      mockAnalyticsStore.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Загрузка')
    })

    it('shows loading state for bookings', async () => {
      mockBookingStore.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Загрузка')
    })

    it('hides loading when data is loaded', async () => {
      mockAnalyticsStore.loading = false
      mockBookingStore.loading = false
      await wrapper.vm.$nextTick()

      // Should show actual data instead of loading
      expect(wrapper.text()).toContain('150') // Total bookings
    })
  })

  describe('Data Fetching', () => {
    it('fetches analytics data on mount', () => {
      expect(mockAnalyticsStore.fetchAnalytics).toHaveBeenCalled()
    })

    it('fetches bookings data on mount', () => {
      expect(mockBookingStore.fetchBookings).toHaveBeenCalled()
    })

    it('fetches services data on mount', () => {
      expect(mockServiceStore.fetchServices).toHaveBeenCalled()
    })

    it('fetches staff data on mount', () => {
      expect(mockStaffStore.fetchStaff).toHaveBeenCalled()
    })
  })

  describe('Charts and Visualizations', () => {
    it('renders revenue chart container', () => {
      const chartContainer = wrapper.find('[data-testid="revenue-chart"]')
      expect(chartContainer.exists()).toBe(true)
    })

    it('renders bookings status chart container', () => {
      const chartContainer = wrapper.find('[data-testid="status-chart"]')
      expect(chartContainer.exists()).toBe(true)
    })

    it('renders category revenue chart container', () => {
      const chartContainer = wrapper.find('[data-testid="category-chart"]')
      expect(chartContainer.exists()).toBe(true)
    })
  })

  describe('Responsive Design', () => {
    it('has responsive grid classes', () => {
      const gridElements = wrapper.findAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)

      // Check for responsive classes
      const hasResponsiveClasses = gridElements.some((el: { classes: () => any[] }) => 
        el.classes().some(cls => cls.includes('md:') || cls.includes('lg:'))
      )
      expect(hasResponsiveClasses).toBe(true)
    })

    it('has responsive spacing classes', () => {
      const spacingElements = wrapper.findAll('[class*="space-"]')
      expect(spacingElements.length).toBeGreaterThan(0)
    })
  })

  describe('Error Handling', () => {
    it('handles analytics fetch errors gracefully', async () => {
      mockAnalyticsStore.fetchAnalytics.mockRejectedValue(new Error('API Error'))

      // Component should still render without crashing
      expect(wrapper.exists()).toBe(true)
    })

    it('shows fallback data when analytics is null', async () => {
      mockAnalyticsStore.analytics = null
      await wrapper.vm.$nextTick()

      // Should show default values or empty state
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Date Range Filtering', () => {
    it('has date range selector', () => {
      const dateInputs = wrapper.findAll('input[type="date"]')
      expect(dateInputs.length).toBeGreaterThanOrEqual(1)
    })

    it('updates analytics when date range changes', async () => {
      const dateInput = wrapper.find('input[type="date"]')
      if (dateInput.exists()) {
        await dateInput.setValue('2024-01-01')
        await dateInput.trigger('change')

        expect(mockAnalyticsStore.fetchAnalytics).toHaveBeenCalledTimes(2) // Once on mount, once on change
      }
    })
  })
})
