import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Login from '@/pages/Login.vue'
import { useAuthStore } from '@/stores/auth'

// Mock router
const mockRouter = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/login', component: Login },
    { path: '/dashboard', component: { template: '<div>Dashboard</div>' } }
  ]
})

describe('Login', () => {
  let wrapper: any
  let pinia: any
  let authStore: any
  let router: any

  beforeEach(async () => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = mockRouter
    authStore = useAuthStore()

    // Mock auth store methods
    vi.spyOn(authStore, 'login')
    vi.spyOn(authStore, 'clearError')
    
    // Mock router methods
    vi.spyOn(router, 'push')
  })

  const createWrapper = (routeQuery = {}) => {
    return mount(Login, {
      global: {
        plugins: [pinia, router],
        mocks: {
          $route: {
            query: routeQuery
          }
        }
      }
    })
  }

  describe('Component Rendering', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('renders login form correctly', () => {
      expect(wrapper.find('h2').text()).toBe('Вход в админ-панель')
      expect(wrapper.text()).toContain('Введите ваши учетные данные для доступа к панели управления')
    })

    it('renders all form fields', () => {
      expect(wrapper.find('#email').exists()).toBe(true)
      expect(wrapper.find('#password').exists()).toBe(true)
      expect(wrapper.find('#remember-me').exists()).toBe(true)
    })

    it('renders form fields with correct attributes', () => {
      const emailInput = wrapper.find('#email')
      const passwordInput = wrapper.find('#password')
      const rememberCheckbox = wrapper.find('#remember-me')

      expect(emailInput.attributes('type')).toBe('email')
      expect(emailInput.attributes('required')).toBeDefined()
      expect(emailInput.attributes('autocomplete')).toBe('email')
      expect(emailInput.attributes('placeholder')).toBe('Email адрес')

      expect(passwordInput.attributes('type')).toBe('password')
      expect(passwordInput.attributes('required')).toBeDefined()
      expect(passwordInput.attributes('autocomplete')).toBe('current-password')
      expect(passwordInput.attributes('placeholder')).toBe('Пароль')

      expect(rememberCheckbox.attributes('type')).toBe('checkbox')
    })

    it('renders submit button', () => {
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.exists()).toBe(true)
      expect(submitButton.text()).toBe('Войти')
    })

    it('renders forgot password link', () => {
      const forgotLink = wrapper.find('a[href="#"]')
      expect(forgotLink.text()).toBe('Забыли пароль?')
    })

    it('renders lock icon', () => {
      const lockIcon = wrapper.find('svg')
      expect(lockIcon.exists()).toBe(true)
    })
  })

  describe('Form Data Binding', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('binds email input correctly', async () => {
      const emailInput = wrapper.find('#email')
      await emailInput.setValue('<EMAIL>')
      
      expect(wrapper.vm.form.email).toBe('<EMAIL>')
    })

    it('binds password input correctly', async () => {
      const passwordInput = wrapper.find('#password')
      await passwordInput.setValue('password123')
      
      expect(wrapper.vm.form.password).toBe('password123')
    })

    it('binds remember checkbox correctly', async () => {
      const rememberCheckbox = wrapper.find('#remember-me')
      
      expect(wrapper.vm.form.remember).toBe(false)
      
      await rememberCheckbox.setChecked(true)
      expect(wrapper.vm.form.remember).toBe(true)
      
      await rememberCheckbox.setChecked(false)
      expect(wrapper.vm.form.remember).toBe(false)
    })
  })

  describe('Form Validation', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('requires email field', () => {
      const emailInput = wrapper.find('#email')
      expect(emailInput.attributes('required')).toBeDefined()
    })

    it('requires password field', () => {
      const passwordInput = wrapper.find('#password')
      expect(passwordInput.attributes('required')).toBeDefined()
    })

    it('validates email format', () => {
      const emailInput = wrapper.find('#email')
      expect(emailInput.attributes('type')).toBe('email')
    })
  })

  describe('Loading States', () => {
    it('shows loading state when auth store is loading', async () => {
      authStore.loading = true
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.text()).toContain('Вход...')
      expect(submitButton.attributes('disabled')).toBeDefined()
      
      const spinner = wrapper.find('.animate-spin')
      expect(spinner.exists()).toBe(true)
    })

    it('disables form inputs when loading', async () => {
      authStore.loading = true
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const emailInput = wrapper.find('#email')
      const passwordInput = wrapper.find('#password')
      
      expect(emailInput.attributes('disabled')).toBeDefined()
      expect(passwordInput.attributes('disabled')).toBeDefined()
    })

    it('shows normal state when not loading', async () => {
      authStore.loading = false
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.text()).toBe('Войти')
      expect(submitButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('Error Display', () => {
    it('shows error message when auth store has error', async () => {
      authStore.error = 'Неверный email или пароль'
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const errorContainer = wrapper.find('.bg-red-50')
      expect(errorContainer.exists()).toBe(true)
      expect(errorContainer.text()).toContain('Ошибка авторизации')
      expect(errorContainer.text()).toContain('Неверный email или пароль')
    })

    it('hides error message when no error', async () => {
      authStore.error = null
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const errorContainer = wrapper.find('.bg-red-50')
      expect(errorContainer.exists()).toBe(false)
    })

    it('renders error icon when error is present', async () => {
      authStore.error = 'Test error'
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const errorIcon = wrapper.find('.bg-red-50 svg')
      expect(errorIcon.exists()).toBe(true)
    })
  })

  describe('Form Submission', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('calls handleLogin when form is submitted', async () => {
      const handleLoginSpy = vi.spyOn(wrapper.vm, 'handleLogin')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(handleLoginSpy).toHaveBeenCalledTimes(1)
    })

    it('calls auth store login with correct credentials', async () => {
      authStore.login.mockResolvedValue({ success: true })
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.clearError).toHaveBeenCalled()
      expect(authStore.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })

    it('redirects to dashboard on successful login', async () => {
      authStore.login.mockResolvedValue({ success: true })
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      await wrapper.vm.$nextTick()
      expect(router.push).toHaveBeenCalledWith('/dashboard')
    })

    it('redirects to specified redirect path on successful login', async () => {
      authStore.login.mockResolvedValue({ success: true })
      wrapper = createWrapper({ redirect: '/calendar' })
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      await wrapper.vm.$nextTick()
      expect(router.push).toHaveBeenCalledWith('/calendar')
    })

    it('does not redirect on failed login', async () => {
      authStore.login.mockResolvedValue({ success: false })
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(router.push).not.toHaveBeenCalled()
    })

    it('handles login errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      authStore.login.mockRejectedValue(new Error('Network error'))
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(consoleSpy).toHaveBeenCalledWith('Login error:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })

    it('prevents submission when already loading', async () => {
      authStore.loading = true
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.login).not.toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('has proper form labels', () => {
      const emailLabel = wrapper.find('label[for="email"]')
      const passwordLabel = wrapper.find('label[for="password"]')
      const rememberLabel = wrapper.find('label[for="remember-me"]')

      expect(emailLabel.exists()).toBe(true)
      expect(passwordLabel.exists()).toBe(true)
      expect(rememberLabel.exists()).toBe(true)
      expect(rememberLabel.text()).toBe('Запомнить меня')
    })

    it('has screen reader only labels for inputs', () => {
      const emailLabel = wrapper.find('label[for="email"]')
      const passwordLabel = wrapper.find('label[for="password"]')

      expect(emailLabel.classes()).toContain('sr-only')
      expect(passwordLabel.classes()).toContain('sr-only')
    })

    it('has proper form structure', () => {
      const form = wrapper.find('form')
      expect(form.exists()).toBe(true)
      expect(form.attributes('novalidate')).toBeUndefined() // Should use browser validation
    })

    it('has proper button type', () => {
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('type')).toBe('submit')
    })

    it('has focus styles for interactive elements', () => {
      const emailInput = wrapper.find('#email')
      const passwordInput = wrapper.find('#password')
      const submitButton = wrapper.find('button[type="submit"]')

      expect(emailInput.classes()).toContain('focus:outline-none')
      expect(passwordInput.classes()).toContain('focus:outline-none')
      expect(submitButton.classes()).toContain('focus:outline-none')
    })
  })

  describe('Styling and Layout', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('has responsive layout classes', () => {
      const container = wrapper.find('.min-h-screen')
      expect(container.exists()).toBe(true)
      expect(container.classes()).toContain('flex')
      expect(container.classes()).toContain('items-center')
      expect(container.classes()).toContain('justify-center')
    })

    it('has proper form styling', () => {
      const formContainer = wrapper.find('.max-w-md')
      expect(formContainer.exists()).toBe(true)
      expect(formContainer.classes()).toContain('w-full')
    })

    it('has disabled state styling', async () => {
      authStore.loading = true
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.classes()).toContain('disabled:opacity-50')
      expect(submitButton.classes()).toContain('disabled:cursor-not-allowed')
    })
  })

  describe('Edge Cases', () => {
    it('handles empty form submission', async () => {
      wrapper = createWrapper()
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.login).toHaveBeenCalledWith({
        email: '',
        password: ''
      })
    })

    it('handles special characters in credentials', async () => {
      wrapper = createWrapper()
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('p@ssw0rd!@#$%')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'p@ssw0rd!@#$%'
      })
    })

    it('handles very long credentials', async () => {
      wrapper = createWrapper()
      
      const longEmail = 'a'.repeat(100) + '@example.com'
      const longPassword = 'p'.repeat(200)
      
      await wrapper.find('#email').setValue(longEmail)
      await wrapper.find('#password').setValue(longPassword)
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.login).toHaveBeenCalledWith({
        email: longEmail,
        password: longPassword
      })
    })

    it('handles undefined redirect query parameter', async () => {
      authStore.login.mockResolvedValue({ success: true })
      wrapper = createWrapper({ redirect: undefined })
      
      await wrapper.find('#email').setValue('<EMAIL>')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      await wrapper.vm.$nextTick()
      expect(router.push).toHaveBeenCalledWith('/dashboard')
    })
  })

  describe('Component State Management', () => {
    it('initializes form with empty values', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.form.email).toBe('')
      expect(wrapper.vm.form.password).toBe('')
      expect(wrapper.vm.form.remember).toBe(false)
    })

    it('computed properties reflect auth store state', async () => {
      wrapper = createWrapper()
      
      authStore.loading = true
      authStore.error = 'Test error'
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.loading).toBe(true)
      expect(wrapper.vm.error).toBe('Test error')
    })

    it('clears error before login attempt', async () => {
      wrapper = createWrapper()
      
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(authStore.clearError).toHaveBeenCalled()
    })
  })
})