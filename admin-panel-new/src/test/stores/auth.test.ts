import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import type { User, LoginCredentials } from '@/stores/auth'

// Mock ApiClient
const mockApiClient = {
  post: vi.fn(),
  get: vi.fn(),
  setAuthToken: vi.fn(),
  clearAuthToken: vi.fn()
}

vi.mock('@/utils/api', () => ({
  ApiClient: mockApiClient
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock Date.now for consistent testing
const mockDateNow = vi.fn()
vi.stubGlobal('Date', {
  ...Date,
  now: mockDateNow
})

describe('Auth Store', () => {
  let authStore: any
  let pinia: any

  const mockUser: User = {
    id: 1,
    name: 'Иван Петров',
    email: '<EMAIL>',
    role: 'admin'
  }

  const mockCredentials: LoginCredentials = {
    email: '<EMAIL>',
    password: 'password123'
  }

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    authStore = useAuthStore()

    // Reset all mocks
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    mockDateNow.mockReturnValue(1640995200000) // Fixed timestamp
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('initializes with correct default state', () => {
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.loading).toBe(false)
      expect(authStore.error).toBeNull()
    })

    it('loads token from localStorage on initialization', () => {
      localStorageMock.getItem.mockReturnValue('stored-token')
      
      // Create new store instance to trigger initialization
      const newStore = useAuthStore()
      
      expect(newStore.token).toBe('stored-token')
    })
  })

  describe('Computed Properties', () => {
    it('isAuthenticated returns false when no token or user', () => {
      expect(authStore.isAuthenticated).toBe(false)
    })

    it('isAuthenticated returns true when both token and user exist', () => {
      authStore.token = 'test-token'
      authStore.user = mockUser
      
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('isAuthenticated returns false when only token exists', () => {
      authStore.token = 'test-token'
      authStore.user = null
      
      expect(authStore.isAuthenticated).toBe(false)
    })

    it('isAuthenticated returns false when only user exists', () => {
      authStore.token = null
      authStore.user = mockUser
      
      expect(authStore.isAuthenticated).toBe(false)
    })

    it('isAdmin returns true for admin user', () => {
      authStore.user = { ...mockUser, role: 'admin' }
      
      expect(authStore.isAdmin).toBe(true)
    })

    it('isAdmin returns false for non-admin user', () => {
      authStore.user = { ...mockUser, role: 'user' }
      
      expect(authStore.isAdmin).toBe(false)
    })

    it('isAdmin returns false when no user', () => {
      authStore.user = null
      
      expect(authStore.isAdmin).toBe(false)
    })
  })

  describe('Login Action', () => {
    it('successfully logs in with valid credentials', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'auth-token-123',
          user: mockUser
        }
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      const result = await authStore.login(mockCredentials)
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/api/admin/login', mockCredentials)
      expect(authStore.token).toBe('auth-token-123')
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.loading).toBe(false)
      expect(authStore.error).toBeNull()
      expect(result).toEqual({ success: true })
    })

    it('saves auth data to localStorage on successful login', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'auth-token-123',
          user: mockUser
        }
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      await authStore.login(mockCredentials)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('authToken', 'auth-token-123')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('authUser', JSON.stringify(mockUser))
      expect(localStorageMock.setItem).toHaveBeenCalledWith('authLoginTime', '1640995200000')
    })

    it('sets auth token in ApiClient on successful login', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'auth-token-123',
          user: mockUser
        }
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      await authStore.login(mockCredentials)
      
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('auth-token-123')
    })

    it('handles login failure with error message', async () => {
      const mockResponse = {
        success: false,
        error: 'Неверный email или пароль'
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      const result = await authStore.login(mockCredentials)
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.error).toBe('Неверный email или пароль')
      expect(authStore.loading).toBe(false)
      expect(result).toEqual({ success: false, message: 'Неверный email или пароль' })
    })

    it('handles login failure with message field', async () => {
      const mockResponse = {
        success: false,
        message: 'Аккаунт заблокирован'
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      const result = await authStore.login(mockCredentials)
      
      expect(authStore.error).toBe('Аккаунт заблокирован')
      expect(result).toEqual({ success: false, message: 'Аккаунт заблокирован' })
    })

    it('handles network errors', async () => {
      mockApiClient.post.mockRejectedValue(new Error('Network error'))
      
      const result = await authStore.login(mockCredentials)
      
      expect(authStore.error).toBe('Network error')
      expect(authStore.loading).toBe(false)
      expect(result).toEqual({ success: false, message: 'Network error' })
    })

    it('handles unknown errors', async () => {
      mockApiClient.post.mockRejectedValue({})
      
      const result = await authStore.login(mockCredentials)
      
      expect(authStore.error).toBe('Ошибка сети')
      expect(result).toEqual({ success: false, message: 'Ошибка сети' })
    })

    it('sets loading state during login process', async () => {
      let resolvePromise: (value: any) => void
      const loginPromise = new Promise((resolve) => {
        resolvePromise = resolve
      })
      
      mockApiClient.post.mockReturnValue(loginPromise)
      
      const loginCall = authStore.login(mockCredentials)
      
      expect(authStore.loading).toBe(true)
      
      resolvePromise!({ success: true, data: { token: 'test', user: mockUser } })
      await loginCall
      
      expect(authStore.loading).toBe(false)
    })

    it('clears previous error before login attempt', async () => {
      authStore.error = 'Previous error'
      
      mockApiClient.post.mockResolvedValue({
        success: true,
        data: { token: 'test', user: mockUser }
      })
      
      await authStore.login(mockCredentials)
      
      expect(authStore.error).toBeNull()
    })
  })

  describe('Logout Action', () => {
    beforeEach(() => {
      // Set up authenticated state
      authStore.user = mockUser
      authStore.token = 'test-token'
      authStore.error = 'Some error'
    })

    it('clears all auth state', () => {
      authStore.logout()
      
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.error).toBeNull()
    })

    it('removes auth data from localStorage', () => {
      authStore.logout()
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('authToken')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('authUser')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('authLoginTime')
    })

    it('clears auth token in ApiClient', () => {
      authStore.logout()
      
      expect(mockApiClient.clearAuthToken).toHaveBeenCalled()
    })
  })

  describe('Initialize Auth', () => {
    it('restores auth state from valid localStorage data', () => {
      const currentTime = 1640995200000
      const loginTime = currentTime - (10 * 24 * 60 * 60 * 1000) // 10 days ago
      
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'authToken': return 'stored-token'
          case 'authUser': return JSON.stringify(mockUser)
          case 'authLoginTime': return loginTime.toString()
          default: return null
        }
      })
      
      mockDateNow.mockReturnValue(currentTime)
      
      authStore.initializeAuth()
      
      expect(authStore.token).toBe('stored-token')
      expect(authStore.user).toEqual(mockUser)
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('stored-token')
    })

    it('clears auth state when token is expired', () => {
      const currentTime = 1640995200000
      const loginTime = currentTime - (35 * 24 * 60 * 60 * 1000) // 35 days ago (expired)
      
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'authToken': return 'stored-token'
          case 'authUser': return JSON.stringify(mockUser)
          case 'authLoginTime': return loginTime.toString()
          default: return null
        }
      })
      
      mockDateNow.mockReturnValue(currentTime)
      
      authStore.initializeAuth()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('authToken')
    })

    it('clears auth state when localStorage data is incomplete', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'authToken': return 'stored-token'
          case 'authUser': return null // Missing user data
          case 'authLoginTime': return '1640995200000'
          default: return null
        }
      })
      
      authStore.initializeAuth()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
    })
  })

  describe('Check Auth', () => {
    it('returns false and logs out when no token', async () => {
      authStore.token = null
      
      const result = await authStore.checkAuth()
      
      expect(result).toBe(false)
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
    })

    it('returns false and logs out when token is expired', async () => {
      authStore.token = 'test-token'
      
      // Mock isTokenExpired to return true
      const currentTime = 1640995200000
      const loginTime = currentTime - (35 * 24 * 60 * 60 * 1000) // 35 days ago
      
      localStorageMock.getItem.mockReturnValue(loginTime.toString())
      mockDateNow.mockReturnValue(currentTime)
      
      const result = await authStore.checkAuth()
      
      expect(result).toBe(false)
      expect(authStore.token).toBeNull()
    })

    it('returns true when token is valid', async () => {
      authStore.token = 'valid-token'
      
      // Mock valid token
      localStorageMock.getItem.mockReturnValue('1640995200000')
      mockDateNow.mockReturnValue(1640995200000 + (10 * 24 * 60 * 60 * 1000)) // 10 days later
      
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: mockUser
      })
      
      const result = await authStore.checkAuth()
      
      expect(result).toBe(true)
      expect(authStore.user).toEqual(mockUser)
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('valid-token')
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/admin/me')
    })

    it('returns false and logs out when API check fails', async () => {
      authStore.token = 'invalid-token'
      
      // Mock valid token time but invalid API response
      localStorageMock.getItem.mockReturnValue('1640995200000')
      mockDateNow.mockReturnValue(1640995200000 + (10 * 24 * 60 * 60 * 1000))
      
      mockApiClient.get.mockResolvedValue({
        success: false
      })
      
      const result = await authStore.checkAuth()
      
      expect(result).toBe(false)
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
    })

    it('returns false and logs out when API throws error', async () => {
      authStore.token = 'test-token'
      
      // Mock valid token time but API error
      localStorageMock.getItem.mockReturnValue('1640995200000')
      mockDateNow.mockReturnValue(1640995200000 + (10 * 24 * 60 * 60 * 1000))
      
      mockApiClient.get.mockRejectedValue(new Error('Network error'))
      
      const result = await authStore.checkAuth()
      
      expect(result).toBe(false)
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
    })
  })

  describe('Clear Error', () => {
    it('clears the error state', () => {
      authStore.error = 'Some error message'
      
      authStore.clearError()
      
      expect(authStore.error).toBeNull()
    })
  })

  describe('Token Expiration', () => {
    it('correctly identifies expired tokens', () => {
      const currentTime = 1640995200000
      const expiredLoginTime = currentTime - (35 * 24 * 60 * 60 * 1000) // 35 days ago
      
      localStorageMock.getItem.mockReturnValue(expiredLoginTime.toString())
      mockDateNow.mockReturnValue(currentTime)
      
      const isExpired = authStore.isTokenExpired()
      
      expect(isExpired).toBe(true)
    })

    it('correctly identifies valid tokens', () => {
      const currentTime = 1640995200000
      const validLoginTime = currentTime - (10 * 24 * 60 * 60 * 1000) // 10 days ago
      
      localStorageMock.getItem.mockReturnValue(validLoginTime.toString())
      mockDateNow.mockReturnValue(currentTime)
      
      const isExpired = authStore.isTokenExpired()
      
      expect(isExpired).toBe(false)
    })

    it('treats missing login time as expired', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const isExpired = authStore.isTokenExpired()
      
      expect(isExpired).toBe(true)
    })

    it('handles exactly 30 days as not expired', () => {
      const currentTime = 1640995200000
      const exactlyThirtyDaysAgo = currentTime - (30 * 24 * 60 * 60 * 1000)
      
      localStorageMock.getItem.mockReturnValue(exactlyThirtyDaysAgo.toString())
      mockDateNow.mockReturnValue(currentTime)
      
      const isExpired = authStore.isTokenExpired()
      
      expect(isExpired).toBe(false)
    })
  })

  describe('Edge Cases', () => {
    it('handles malformed JSON in localStorage user data', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'authToken': return 'stored-token'
          case 'authUser': return 'invalid-json'
          case 'authLoginTime': return '1640995200000'
          default: return null
        }
      })
      
      expect(() => authStore.initializeAuth()).not.toThrow()
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
    })

    it('handles invalid login time format', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'authToken': return 'stored-token'
          case 'authUser': return JSON.stringify(mockUser)
          case 'authLoginTime': return 'invalid-timestamp'
          default: return null
        }
      })
      
      authStore.initializeAuth()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
    })

    it('handles concurrent login attempts', async () => {
      const mockResponse = {
        success: true,
        data: { token: 'test-token', user: mockUser }
      }
      
      mockApiClient.post.mockResolvedValue(mockResponse)
      
      // Start multiple login attempts
      const login1 = authStore.login(mockCredentials)
      const login2 = authStore.login(mockCredentials)
      
      const [result1, result2] = await Promise.all([login1, login2])
      
      expect(result1.success).toBe(true)
      expect(result2.success).toBe(true)
      expect(mockApiClient.post).toHaveBeenCalledTimes(2)
    })

    it('handles empty credentials', async () => {
      const emptyCredentials = { email: '', password: '' }
      
      mockApiClient.post.mockResolvedValue({
        success: false,
        error: 'Email и пароль обязательны'
      })
      
      const result = await authStore.login(emptyCredentials)
      
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Email и пароль обязательны')
    })
  })
})